# AI工具导航系统

一个基于 **Vue3 + Python FastAPI** 的高端AI工具导航平台，集成了图片生成、视频生成、语音识别等AI功能。

## 🚀 快速开始

### 方式1: 一键启动(推荐) ⭐

**双击运行**: `启动项目.bat`

选择选项3 - 同时启动前后端，浏览器会自动打开页面！

### 方式2: 分别启动

#### 启动后端
```powershell
# 双击运行
after/start.bat

# 或使用命令行
cd after
python main.py
```

#### 启动前端
```powershell
# 双击运行
before/start-frontend.bat

# 或使用命令行
cd before
python -m http.server 3000
```

访问: http://localhost:3000/html/

## 📦 依赖安装

首次使用需安装Python依赖:

```powershell
cd after
pip install -r requirements.txt
```

需要的包:
- fastapi
- uvicorn
- requests
- pydantic

## 🌐 访问地址

| 服务 | 地址 | 说明 |
|------|------|------|
| **前端页面** | http://localhost:3000/html/ | 主界面 |
| **后端API** | http://localhost:8000 | API服务 |
| **API文档** | http://localhost:8000/docs | Swagger文档 |
| **健康检查** | http://localhost:8000/api/health | 服务状态 |

## ✨ 功能模块

### 1. 图片生成 🎨 ✅ 已上线

- **功能**: 基于火山引擎豆包AI的智能图片生成
- **特性**:
  - 文生图(根据文字描述生成图片)
  - 5种预设比例(1:1, 4:3, 3:4, 16:9, 9:16)
  - 随机种子控制
  - 高分辨率输出(最高2560x2560)
  - 实时预览和下载

### 2. 视频生成 🎬 (待开发)
### 3. 语音识别 🎤 (待开发)
### 4. 项目质量 📊 (待开发)

## 🎯 使用示例

### 图片生成

1. 访问 http://localhost:3000
2. 点击"图片生成"卡片
3. 输入提示词，例如:
   - "一只可爱的橙色小猫坐在花园里"
   - "未来城市，赛博朋克风格，霓虹灯"
   - "水彩画风格的山水画"
4. 选择图片比例
5. 点击"生成图片"等待10-30秒
6. 生成完成后可预览和下载

## 📁 项目结构

```
d:/private/private01/
├── 启动项目.bat              # 一键启动脚本 ⭐
├── before/                   # 前端代码
│   ├── start-frontend.bat   # 前端启动脚本
│   ├── html/                # HTML页面
│   │   ├── index.html       # 首页
│   │   └── image-generation.html  # 图片生成页面
│   ├── css/                 # 样式文件
│   │   ├── main.css
│   │   ├── animations.css
│   │   └── image-generation.css
│   └── js/                  # JavaScript文件
│       ├── main.js
│       ├── meteor.js
│       ├── cursor.js
│       └── image-generation.js
├── after/                   # 后端代码
│   ├── start.bat           # 后端启动脚本
│   ├── main.py             # 主程序
│   ├── requirements.txt    # 依赖清单
│   ├── config/             # 配置文件
│   │   └── config.json
│   └── impl/               # 业务实现
│       ├── image_gen.py    # 图片生成 ✅
│       ├── video_gen.py
│       ├── voice_rec.py
│       └── quality_check.py
├── test/                   # 测试代码
│   ├── test_api.py
│   └── test_image_generation.py
└── doc/                    # 文档
    ├── README.md
    ├── 快速开始.md
    ├── 图片生成功能说明.md
    ├── 服务启动指南.md
    └── 常见问题解决方案.md
```

## 🛠️ 技术栈

### 前端
- **Vue 3** (Composition API, CDN)
- **原生 JavaScript** (ES6+)
- **Canvas API** (流星动画、鼠标拖尾)
- **CSS3** (玻璃态效果、渐变、动画)

### 后端
- **Python 3.8+**
- **FastAPI** (高性能Web框架)
- **Uvicorn** (ASGI服务器)
- **Requests** (HTTP请求)

### AI服务
- **火山引擎豆包AI** (图片生成)
- **模型**: doubao-seedream-4-0-250828

## ⚙️ 配置说明

编辑 `after/config/config.json`:

```json
{
  "api_keys": {
    "image_generation": "您的火山AI密钥"
  },
  "server": {
    "host": "127.0.0.1",
    "port": 8000,
    "cors_origins": ["*"]
  }
}
```

## 🔧 常见问题

### 1. 前端打不开页面
**解决**: 确保前端服务器已启动
```powershell
cd before
python -m http.server 3000
# 访问 http://localhost:3000/html/
```

### 2. 后端无法访问
**解决**: 检查后端是否正常运行
```powershell
# 访问健康检查
http://localhost:8000/api/health
# 应返回 {"status": "healthy"}
```

### 3. 生成图片报错 "Failed to fetch"
**解决**: 
- 确保后端服务已启动
- 检查CORS配置
- 使用HTTP服务器打开前端(不要直接双击HTML)

### 4. 端口被占用
**解决**: 
```powershell
# 查看占用端口的进程
netstat -ano | findstr :8000
netstat -ano | findstr :3000

# 或修改配置文件中的端口号
```

**详细故障排查**: 请查看 `doc/常见问题解决方案.md`

## 📖 文档

- [快速开始](doc/快速开始.md) - 新手入门指南
- [图片生成功能说明](doc/图片生成功能说明.md) - 详细技术文档
- [服务启动指南](doc/服务启动指南.md) - 启动方式和问题排查
- [常见问题解决方案](doc/常见问题解决方案.md) - FAQ
- [API文档](doc/API文档.md) - 接口说明

## ⚠️ 重要提示

1. **图片URL有效期**: 火山AI返回的图片URL仅**24小时有效**，请及时下载
2. **API Key**: 需要有效的火山引擎API Key才能使用图片生成功能
3. **网络要求**: 需要能访问火山引擎API服务

## 🎯 后续开发计划

- [x] 图片生成功能 (已完成 ✅)
- [ ] 图生图功能(图片上传)
- [ ] 图片历史记录(数据库支持)
- [ ] OSS永久存储
- [ ] 视频生成功能
- [ ] 语音识别功能
- [ ] 代码质量分析
- [ ] 用户认证系统
- [ ] 批量生成功能

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

---

**享受您的AI创作之旅！** 🚀✨
