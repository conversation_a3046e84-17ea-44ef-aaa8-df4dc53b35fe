"""
图片生成功能测试脚本
测试火山AI图片生成接口
"""
import sys
import os

# 添加父目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'after'))

from impl.image_gen import ImageGenerator
import json


def test_text_to_image():
    """测试文生图功能"""
    print("\n" + "="*60)
    print("测试1: 文生图 (Text-to-Image)")
    print("="*60)
    
    generator = ImageGenerator()
    
    result = generator.generate(
        prompt="一只可爱的橙色小猫坐在花园里,阳光明媚,花朵盛开",
        width=2048,
        height=2048,
        seed=12345
    )
    
    print("\n响应结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    
    if result['success']:
        print("\n✅ 测试通过!")
        print(f"图片URL: {result['data']['image_url']}")
        print(f"耗时: {result['data']['duration_ms']}ms")
    else:
        print("\n❌ 测试失败!")
        print(f"错误信息: {result['message']}")
    
    return result['success']


def test_different_sizes():
    """测试不同尺寸"""
    print("\n" + "="*60)
    print("测试2: 不同尺寸")
    print("="*60)
    
    generator = ImageGenerator()
    sizes = generator.get_preset_sizes()
    
    print(f"\n可用预设尺寸: {len(sizes)}种")
    for size in sizes:
        print(f"  - {size['name']}: {size['width']}x{size['height']}")
    
    # 测试一个尺寸
    test_size = sizes[0]
    print(f"\n测试尺寸: {test_size['name']} ({test_size['width']}x{test_size['height']})")
    
    result = generator.generate(
        prompt="未来科技城市,霓虹灯光,赛博朋克风格",
        width=test_size['width'],
        height=test_size['height']
    )
    
    if result['success']:
        print("✅ 测试通过!")
    else:
        print("❌ 测试失败!")
        print(f"错误: {result['message']}")
    
    return result['success']


def test_prompt_validation():
    """测试提示词验证"""
    print("\n" + "="*60)
    print("测试3: 提示词验证")
    print("="*60)
    
    generator = ImageGenerator()
    
    # 测试空提示词
    print("\n测试空提示词...")
    is_valid, message = generator.validate_prompt("")
    print(f"结果: {message}")
    assert not is_valid, "空提示词应该验证失败"
    print("✅ 通过")
    
    # 测试过长提示词
    print("\n测试过长提示词...")
    long_prompt = "测试" * 1001
    is_valid, message = generator.validate_prompt(long_prompt)
    print(f"结果: {message}")
    assert not is_valid, "过长提示词应该验证失败"
    print("✅ 通过")
    
    # 测试正常提示词
    print("\n测试正常提示词...")
    is_valid, message = generator.validate_prompt("一只可爱的猫咪")
    print(f"结果: {message}")
    assert is_valid, "正常提示词应该验证通过"
    print("✅ 通过")
    
    return True


def test_error_handling():
    """测试错误处理"""
    print("\n" + "="*60)
    print("测试4: 错误处理")
    print("="*60)
    
    generator = ImageGenerator()
    
    # 测试空提示词请求
    print("\n测试空提示词请求...")
    result = generator.generate(prompt="")
    
    assert not result['success'], "空提示词应该返回失败"
    print(f"错误信息: {result['message']}")
    print("✅ 测试通过!")
    
    return True


def run_all_tests():
    """运行所有测试"""
    print("\n" + "="*60)
    print("🚀 开始运行图片生成功能测试")
    print("="*60)
    
    tests = [
        ("提示词验证", test_prompt_validation),
        ("错误处理", test_error_handling),
        ("文生图", test_text_to_image),
        ("不同尺寸", test_different_sizes),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"\n❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 打印测试汇总
    print("\n" + "="*60)
    print("📊 测试结果汇总")
    print("="*60)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name}: {status}")
    
    total = len(results)
    passed = sum(1 for _, success in results if success)
    
    print("\n" + "="*60)
    print(f"总计: {passed}/{total} 通过")
    print("="*60)
    
    return passed == total


if __name__ == "__main__":
    try:
        all_passed = run_all_tests()
        
        if all_passed:
            print("\n🎉 所有测试通过!")
            sys.exit(0)
        else:
            print("\n⚠️ 部分测试失败!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 测试过程中发生严重错误: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
