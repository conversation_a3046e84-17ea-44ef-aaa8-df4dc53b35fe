"""
API接口测试脚本
用于测试后端各个接口的功能
"""
import requests
import json


BASE_URL = "http://localhost:8000"


def test_root():
    """测试根路径"""
    print("\n测试根路径...")
    response = requests.get(f"{BASE_URL}/")
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")


def test_image_generation():
    """测试图片生成接口"""
    print("\n测试图片生成...")
    data = {
        "prompt": "一只可爱的猫咪在星空下",
        "style": "realistic"
    }
    response = requests.post(f"{BASE_URL}/api/image-generation", json=data)
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")


def test_video_generation():
    """测试视频生成接口"""
    print("\n测试视频生成...")
    data = {
        "prompt": "未来城市的日出",
        "duration": 5
    }
    response = requests.post(f"{BASE_URL}/api/video-generation", json=data)
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")


def test_voice_recognition():
    """测试语音识别接口"""
    print("\n测试语音识别...")
    data = {
        "audio_data": "base64_encoded_audio_data",
        "language": "zh"
    }
    response = requests.post(f"{BASE_URL}/api/voice-recognition", json=data)
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")


def test_quality_check():
    """测试代码质量检查接口"""
    print("\n测试代码质量检查...")
    data = {
        "code": "def hello():\n    print('Hello, World!')",
        "language": "python"
    }
    response = requests.post(f"{BASE_URL}/api/quality-check", json=data)
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")


def test_health():
    """测试健康检查接口"""
    print("\n测试健康检查...")
    response = requests.get(f"{BASE_URL}/api/health")
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")


if __name__ == "__main__":
    print("=" * 60)
    print("AI工具导航API接口测试")
    print("=" * 60)
    print("请确保后端服务已启动 (python after/main.py)")
    print("=" * 60)
    
    try:
        test_root()
        test_health()
        test_image_generation()
        test_video_generation()
        test_voice_recognition()
        test_quality_check()
        
        print("\n" + "=" * 60)
        print("所有测试完成!")
        print("=" * 60)
        
    except requests.exceptions.ConnectionError:
        print("\n错误: 无法连接到后端服务!")
        print("请先启动后端服务: python after/main.py")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
