#!/bin/bash

# 设置脚本所在目录为工作目录
SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 清屏并显示菜单
clear
echo ""
echo "========================================"
echo "   AI工具导航 - 项目启动器"
echo "========================================"
echo ""
echo "请选择要启动的服务:"
echo ""
echo "[1] 启动后端服务 (端口: 8000)"
echo "[2] 启动前端服务 (端口: 3000)"
echo "[3] 同时启动前后端 (推荐)"
echo "[4] 退出"
echo ""
echo "========================================"
read -p "请输入选项 (1-4): " choice

case $choice in
    1)
        echo ""
        echo "正在启动后端服务..."
        cd "$SCRIPT_DIR/after"
        
        # 在新的终端窗口中启动后端服务
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS 使用 Terminal.app
            osascript -e "tell application \"Terminal\" to do script \"cd '$SCRIPT_DIR/after' && python main.py\""
        else
            # Linux 尝试使用常见的终端模拟器
            if command -v gnome-terminal &> /dev/null; then
                gnome-terminal -- bash -c "cd '$SCRIPT_DIR/after' && python main.py; exec bash"
            elif command -v xterm &> /dev/null; then
                xterm -e "cd '$SCRIPT_DIR/after' && python main.py; exec bash" &
            else
                echo -e "${YELLOW}警告: 无法打开新终端窗口,在后台启动...${NC}"
                python main.py &
            fi
        fi
        
        sleep 2
        echo ""
        echo -e "${GREEN}✓ 后端服务已在新窗口启动${NC}"
        echo "  访问: http://localhost:8000/docs"
        ;;
        
    2)
        echo ""
        echo "正在启动前端服务..."
        cd "$SCRIPT_DIR/before"
        
        # 在新的终端窗口中启动前端服务
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS 使用 Terminal.app
            osascript -e "tell application \"Terminal\" to do script \"cd '$SCRIPT_DIR/before' && python -m http.server 3000\""
        else
            # Linux 尝试使用常见的终端模拟器
            if command -v gnome-terminal &> /dev/null; then
                gnome-terminal -- bash -c "cd '$SCRIPT_DIR/before' && python -m http.server 3000; exec bash"
            elif command -v xterm &> /dev/null; then
                xterm -e "cd '$SCRIPT_DIR/before' && python -m http.server 3000; exec bash" &
            else
                echo -e "${YELLOW}警告: 无法打开新终端窗口,在后台启动...${NC}"
                python -m http.server 3000 &
            fi
        fi
        
        sleep 2
        echo ""
        echo -e "${GREEN}✓ 前端服务已在新窗口启动${NC}"
        echo "  访问: http://localhost:3000/html/"
        ;;
        
    3)
        echo ""
        echo "正在同时启动前后端服务..."
        echo ""
        
        echo "[1/2] 启动后端服务..."
        cd "$SCRIPT_DIR/after"
        
        # 启动后端服务
        if [[ "$OSTYPE" == "darwin"* ]]; then
            osascript -e "tell application \"Terminal\" to do script \"cd '$SCRIPT_DIR/after' && python main.py\""
        else
            if command -v gnome-terminal &> /dev/null; then
                gnome-terminal -- bash -c "cd '$SCRIPT_DIR/after' && python main.py; exec bash"
            elif command -v xterm &> /dev/null; then
                xterm -e "cd '$SCRIPT_DIR/after' && python main.py; exec bash" &
            else
                cd "$SCRIPT_DIR/after" && python main.py &
            fi
        fi
        
        sleep 2
        
        echo "[2/2] 启动前端服务..."
        cd "$SCRIPT_DIR/before"
        
        # 启动前端服务
        if [[ "$OSTYPE" == "darwin"* ]]; then
            osascript -e "tell application \"Terminal\" to do script \"cd '$SCRIPT_DIR/before' && python -m http.server 3000\""
        else
            if command -v gnome-terminal &> /dev/null; then
                gnome-terminal -- bash -c "cd '$SCRIPT_DIR/before' && python -m http.server 3000; exec bash"
            elif command -v xterm &> /dev/null; then
                xterm -e "cd '$SCRIPT_DIR/before' && python -m http.server 3000; exec bash" &
            else
                cd "$SCRIPT_DIR/before" && python -m http.server 3000 &
            fi
        fi
        
        sleep 2
        
        echo ""
        echo "========================================"
        echo -e "${GREEN}✓ 前后端服务已全部启动!${NC}"
        echo "========================================"
        echo ""
        echo "前端: http://localhost:3000/html/"
        echo "后端: http://localhost:8000"
        echo "API文档: http://localhost:8000/docs"
        echo ""
        echo "提示: 请保持两个服务窗口打开"
        echo "      按 Ctrl+C 可停止相应服务"
        echo ""
        
        sleep 3
        
        # 在默认浏览器中打开前端页面
        if [[ "$OSTYPE" == "darwin"* ]]; then
            open "http://localhost:3000/html/"
        elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
            if command -v xdg-open &> /dev/null; then
                xdg-open "http://localhost:3000/html/"
            fi
        fi
        ;;
        
    4)
        echo ""
        echo "退出..."
        exit 0
        ;;
        
    *)
        echo ""
        echo -e "${RED}❌ 无效选项,请重新运行脚本${NC}"
        sleep 2
        ;;
esac

echo ""
read -p "按 Enter 键退出..."

