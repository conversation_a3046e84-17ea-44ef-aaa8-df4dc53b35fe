# AI工具导航系统

一个高端风格的AI工具导航平台，提供图片生成、视频生成、声音识别、项目质量检查等AI功能。

## 项目特点

- 🎨 **精美UI设计**: 流星划过动画背景 + 鼠标光点拖尾效果
- 🚀 **前后端分离**: Vue3前端 + Python FastAPI后端
- 🔌 **易于扩展**: 预留AI接口对接位置，方便后续集成
- 📦 **模块化设计**: 每个AI功能独立模块，便于维护

## 项目结构

```
d:/private/private01/
├── before/              # 前端代码
│   ├── html/           # 页面文件
│   │   └── index.html  # 主页面
│   ├── css/            # 样式文件
│   │   ├── main.css    # 主样式
│   │   └── animations.css  # 动画样式
│   └── js/             # JavaScript文件
│       ├── main.js     # Vue主程序
│       ├── meteor.js   # 流星动画
│       └── cursor.js   # 鼠标拖尾
├── after/              # 后端代码
│   ├── config/         # 配置文件
│   │   └── config.json # API密钥等配置
│   ├── impl/           # 业务逻辑
│   │   ├── image_gen.py      # 图片生成
│   │   ├── video_gen.py      # 视频生成
│   │   ├── voice_rec.py      # 声音识别
│   │   └── quality_check.py  # 项目质量
│   ├── main.py         # 后端主程序
│   └── requirements.txt # Python依赖
├── test/               # 测试代码
│   └── test_api.py     # API测试脚本
└── doc/                # 文档
    └── README.md       # 项目文档
```

## 快速开始

### 前端启动

1. 直接打开浏览器访问:
```bash
# 方式1: 直接打开HTML文件
d:/private/private01/before/html/index.html

# 方式2: 使用本地服务器(推荐)
cd d:/private/private01/before/html
python -m http.server 3000
# 访问 http://localhost:3000
```

### 后端启动

1. 安装Python依赖:
```bash
cd d:/private/private01/after
pip install -r requirements.txt
```

2. 配置API密钥:
编辑 `config/config.json` 文件，填入您的AI服务API密钥

3. 启动后端服务:
```bash
python main.py
```

服务启动后:
- API地址: http://localhost:8000
- API文档: http://localhost:8000/docs

### 运行测试

```bash
cd d:/private/private01
python test/test_api.py
```

## 功能模块

### 1. 图片生成 🎨 ✅
- **状态**: 已上线
- **接口**: `/api/image-generation`
- **功能**: 基于火山引擎豆包AI的智能图片生成
- **特性**:
  - 文生图(根据文字描述生成图片)
  - 多种预设尺寸(1:1, 4:3, 3:4, 16:9, 9:16)
  - 随机种子控制
  - 高分辨率输出(最高2560x2560)
- **文档**: 详见 `doc/图片生成功能说明.md` 和 `doc/快速开始.md`

### 2. 视频生成 🎬
- **接口**: `/api/video-generation`
- **功能**: 文字/图片转视频
- **状态**: 待对接AI服务

### 3. 声音识别 🎤
- **接口**: `/api/voice-recognition`
- **功能**: 语音转文字
- **状态**: 待对接AI服务

### 4. 项目质量 📊
- **接口**: `/api/quality-check`
- **功能**: 代码质量分析
- **状态**: 待对接AI服务

## 技术栈

### 前端
- Vue 3 (CDN引入,Composition API)
- 原生JavaScript (ES6+)
- Canvas API (动画效果)
- CSS3 (渐变、动画、玻璃态效果)

### 后端
- Python 3.8+
- FastAPI (Web框架)
- Uvicorn (ASGI服务器)
- Pydantic (数据验证)
- Requests (HTTP请求)

### AI服务
- 火山引擎豆包AI (图片生成)
- 模型: doubao-seedream-4-0-250828

## 后续开发计划

- [x] 对接图片生成AI服务 (已完成 ✅)
- [ ] 图生图功能(需实现图片上传)
- [ ] 图片历史记录(需数据库支持)
- [ ] OSS永久存储(避免24小时URL过期)
- [ ] 对接视频生成AI服务
- [ ] 对接语音识别AI服务
- [ ] 对接代码质量分析AI服务
- [ ] 添加用户认证系统
- [ ] 批量生成功能
- [ ] 优化性能和用户体验

## API文档

启动后端服务后，访问 http://localhost:8000/docs 查看完整的交互式API文档。

## 配置说明

编辑 `after/config/config.json`:

```json
{
  "api_keys": {
    "image_generation": "您的图片生成API密钥",
    "video_generation": "您的视频生成API密钥",
    "voice_recognition": "您的语音识别API密钥",
    "quality_check": "您的代码分析API密钥"
  },
  "server": {
    "host": "0.0.0.0",
    "port": 8000,
    "debug": true
  }
}
```

## 注意事项

1. 首次运行需要安装Python依赖
2. 配置文件中的API密钥需要自行申请
3. 前端使用CDN引入Vue3，需要网络连接
4. 建议使用现代浏览器以获得最佳体验

## 许可证

MIT License

## 联系方式

如有问题或建议，欢迎提Issue。
