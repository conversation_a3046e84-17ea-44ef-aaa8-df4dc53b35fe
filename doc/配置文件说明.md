# 配置文件说明

## 📁 文件位置
`after/config/config.json`

## 🏗️ 配置结构

配置文件采用**以AI服务提供商为维度**的组织方式,便于管理多个API提供商和模型。

### 整体结构

```json
{
  "ai_providers": { ... },      // AI服务提供商配置
  "active_providers": { ... },  // 当前激活的提供商
  "database": { ... },          // 数据库配置
  "server": { ... }             // 服务器配置
}
```

---

## 1️⃣ AI服务提供商配置 (`ai_providers`)

以**提供商为维度**,每个提供商包含API密钥、基础URL和模型配置。

### 火山引擎 (volcengine)

```json
{
  "ai_providers": {
    "volcengine": {
      "api_key": "YOUR_VOLCENGINE_API_KEY",
      "base_url": "https://ark.cn-beijing.volces.com/api/v3",
      "timeout": 60,
      "models": {
        "image_generation": {
          "model_name": "doubao-seedream-4-0-250828",
          "model_id": "doubao-seedream-4-0-250828",
          "max_resolution": "2560x2560",
          "preset_sizes": [
            {"name": "1:1", "width": 2048, "height": 2048},
            {"name": "4:3", "width": 2304, "height": 1728},
            {"name": "3:4", "width": 1728, "height": 2304},
            {"name": "16:9", "width": 2560, "height": 1440},
            {"name": "9:16", "width": 1440, "height": 2560}
          ]
        }
      }
    }
  }
}
```

**字段说明**:
- `api_key`: 火山引擎的API密钥
- `base_url`: API基础地址
- `timeout`: 请求超时时间(秒)
- `models.image_generation`: 图片生成模型配置
  - `model_name`: 模型显示名称
  - `model_id`: 实际调用的模型ID
  - `max_resolution`: 最大分辨率
  - `preset_sizes`: 预设尺寸列表

### OpenAI (待对接示例)

```json
{
  "ai_providers": {
    "openai": {
      "api_key": "YOUR_OPENAI_API_KEY_HERE",
      "base_url": "https://api.openai.com/v1",
      "timeout": 60,
      "models": {
        "image_generation": {
          "model_name": "dall-e-3",
          "model_id": "dall-e-3",
          "max_resolution": "1024x1024"
        }
      }
    }
  }
}
```

### 添加新的提供商

只需在 `ai_providers` 下添加新的配置块:

```json
{
  "ai_providers": {
    "your_provider": {
      "api_key": "YOUR_API_KEY",
      "base_url": "https://api.example.com",
      "timeout": 60,
      "models": {
        "image_generation": {
          "model_name": "model-name",
          "model_id": "model-id",
          "max_resolution": "2048x2048"
        }
      }
    }
  }
}
```

---

## 2️⃣ 激活的提供商 (`active_providers`)

指定每个功能当前使用哪个提供商。

```json
{
  "active_providers": {
    "image_generation": "volcengine",    // 图片生成使用火山引擎
    "video_generation": null,            // 暂未对接
    "voice_recognition": null,           // 暂未对接
    "quality_check": null                // 暂未对接
  }
}
```

**切换提供商**: 只需修改对应的值即可,例如:
```json
"image_generation": "openai"  // 切换到OpenAI
```

---

## 3️⃣ 数据库配置 (`database`)

用于存储图片历史、用户数据等(当前未启用)。

```json
{
  "database": {
    "host": "localhost",
    "port": 5432,
    "username": "your_username",
    "password": "your_password",
    "database": "ai_tools_db"
  }
}
```

---

## 4️⃣ 服务器配置 (`server`)

```json
{
  "server": {
    "host": "127.0.0.1",           // 监听地址
    "port": 8000,                   // 监听端口
    "debug": false,                 // 调试模式
    "cors_origins": ["*"]           // 允许的CORS来源
  }
}
```

**字段说明**:
- `host`: 
  - `127.0.0.1` - 仅本机访问
  - `0.0.0.0` - 允许外部访问
- `port`: API服务端口
- `debug`: 
  - `true` - 开启热重载(开发)
  - `false` - 关闭热重载(生产)
- `cors_origins`: 
  - `["*"]` - 允许所有来源(开发)
  - `["http://yourdomain.com"]` - 指定域名(生产)

---

## 🎯 配置示例

### 完整配置示例

```json
{
  "ai_providers": {
    "volcengine": {
      "api_key": "480b4cd8-2d62-423d-8a59-bb6e47cf3776",
      "base_url": "https://ark.cn-beijing.volces.com/api/v3",
      "timeout": 60,
      "models": {
        "image_generation": {
          "model_name": "doubao-seedream-4-0-250828",
          "model_id": "doubao-seedream-4-0-250828",
          "max_resolution": "2560x2560",
          "preset_sizes": [
            {"name": "1:1", "width": 2048, "height": 2048},
            {"name": "4:3", "width": 2304, "height": 1728}
          ]
        }
      }
    },
    "openai": {
      "api_key": "sk-xxxxxxxxxxxxx",
      "base_url": "https://api.openai.com/v1",
      "timeout": 60,
      "models": {
        "image_generation": {
          "model_name": "dall-e-3",
          "model_id": "dall-e-3",
          "max_resolution": "1024x1024"
        }
      }
    }
  },
  "active_providers": {
    "image_generation": "volcengine"
  },
  "database": {
    "host": "localhost",
    "port": 5432,
    "username": "admin",
    "password": "password123",
    "database": "ai_tools_db"
  },
  "server": {
    "host": "127.0.0.1",
    "port": 8000,
    "debug": false,
    "cors_origins": ["*"]
  }
}
```

---

## 🔧 常用操作

### 1. 添加新的API密钥

编辑 `ai_providers.volcengine.api_key`:
```json
"api_key": "YOUR_NEW_API_KEY"
```

### 2. 切换图片生成提供商

编辑 `active_providers.image_generation`:
```json
"image_generation": "openai"  // 从volcengine切换到openai
```

### 3. 修改服务端口

编辑 `server.port`:
```json
"port": 8001  // 改为8001端口
```

### 4. 添加新模型

在对应提供商的 `models` 下添加:
```json
"models": {
  "image_generation": { ... },
  "video_generation": {
    "model_name": "new-model",
    "model_id": "new-model-id"
  }
}
```

---

## ⚠️ 注意事项

1. **JSON格式**: 确保JSON格式正确,注意逗号和引号
2. **API密钥安全**: 不要将配置文件提交到公开仓库
3. **重启服务**: 修改配置后需要重启后端服务才能生效
4. **提供商切换**: 切换提供商前确保已配置好对应的API密钥

---

## 🚀 优势

### 1. 清晰的层级结构
```
提供商 → 模型配置 → 具体参数
```

### 2. 易于扩展
添加新提供商只需增加配置块,不影响现有配置。

### 3. 灵活切换
通过 `active_providers` 快速切换不同提供商。

### 4. 统一管理
所有API密钥和配置集中管理,便于维护。

---

## 📝 配置验证

使用以下命令验证配置文件格式:

```powershell
# Python验证
python -m json.tool after/config/config.json

# 或
cd after
python -c "import json; print(json.load(open('config/config.json')))"
```

---

## 🔗 相关文档

- [快速开始](快速开始.md)
- [图片生成功能说明](图片生成功能说明.md)
- [服务启动指南](服务启动指南.md)
