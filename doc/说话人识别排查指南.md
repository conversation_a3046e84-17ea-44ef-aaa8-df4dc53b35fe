# 说话人识别功能排查指南

## 问题描述

**症状**: 不同人说话没有区分出来,都显示为同一个说话人

## 可能原因分析

### 1. 讯飞账号权限问题 ⭐最可能

讯飞的**说话人盲分功能**可能需要:
- ✅ 开通专门的服务权限
- ✅ 使用特定的APPID
- ✅ 账号需要实名认证或企业认证

**检查方法**:
1. 登录讯飞开放平台: https://console.xfyun.cn/
2. 进入"控制台" → "我的应用" → 选择你的应用
3. 查看"服务管理" → "语音听写(流式版)"
4. 确认是否开通了"说话人分离"功能

### 2. API参数配置问题

当前配置:
```json
{
  "vinfo": 1,  // 说话人识别开关
  "domain": "iat",  // 业务领域
  "language": "zh_cn",
  "accent": "mandarin"
}
```

**可能需要调整**:
- `vinfo`: 1 = 开启, 0 = 关闭
- 可能需要特定的`domain`参数

### 3. 返回数据结构问题

说话人信息在讯飞返回的`rl`字段中:
```json
{
  "data": {
    "result": {
      "ws": [
        {
          "cw": [{"w": "你好"}],
          "rl": 0  // ← 说话人ID (0, 1, 2...)
        }
      ]
    }
  }
}
```

如果`rl`字段不存在,说明:
- ❌ 功能未开通
- ❌ 该APPID不支持
- ❌ 需要申请权限

## 调试步骤

### 步骤1: 重启后端查看日志

1. **关闭当前后端服务** (Ctrl+C)

2. **重新启动**:
   ```bash
   cd after
   python main.py
   ```

3. **开始录音测试**,观察控制台输出:

   **正常情况** (说话人识别生效):
   ```
   📥 讯飞返回数据: {"code":0,"data":{"result":{"ws":[{"cw":[{"w":"你好"}],"rl":0}]}}}
   🎤 检测到说话人ID: 0
   ✅ 识别结果 - 文本: 你好, 说话人: 0
   ```

   **异常情况** (说话人识别未生效):
   ```
   📥 讯飞返回数据: {"code":0,"data":{"result":{"ws":[{"cw":[{"w":"你好"}]}]}}}
   ⚠️ 当前ws_item无rl字段: {"cw":[{"w":"你好"}]}
   ✅ 识别结果 - 文本: 你好, 说话人: None
   ```

### 步骤2: 检查讯飞控制台

1. **访问**: https://console.xfyun.cn/

2. **查看应用详情**:
   - APPID: `4ff6d5e8`
   - 服务: 语音听写(流式版)

3. **确认功能开通状态**:
   - 基础识别: ✅ (已开通)
   - 说话人分离: ❓ (需确认)

4. **如果未开通**:
   - 点击"开通"或"试用"
   - 可能需要填写申请信息

### 步骤3: 测试替代方案

如果讯飞账号确实不支持说话人识别,可以:

**方案A: 申请开通权限**
```
联系讯飞客服申请说话人分离功能
可能需要企业认证或付费
```

**方案B: 手动标注说话人**
```javascript
// 前端添加手动切换说话人按钮
<button onclick="switchSpeaker(0)">说话人1</button>
<button onclick="switchSpeaker(1)">说话人2</button>
```

**方案C: 使用其他服务商**
```
- 阿里云ASR (支持说话人分离)
- 腾讯云ASR (支持说话人分离)
- Microsoft Azure (支持说话人识别)
```

## 快速验证方法

### 测试1: 单人长时间说话

如果单人说话时`speaker_id`一直为`0`或`None`,说明:
- ✅ 可能是正常的(单人场景)
- ⚠️ 也可能是功能未生效

### 测试2: 两人交替说话

**测试方式**:
1. 人A说一句话
2. 暂停2-3秒
3. 人B说一句话
4. 暂停2-3秒
5. 人A再说一句

**预期结果**:
```
人A: "大家好" → 说话人1
人B: "你好吗" → 说话人2
人A: "我很好" → 说话人1
```

**实际结果**:
- ✅ 正常: 不同人显示不同颜色和编号
- ❌ 异常: 所有人都是相同颜色/编号

## 临时解决方案

如果讯飞账号确实不支持,可以先使用以下方案:

### 方案1: VAD静音检测切换

根据静音时长自动切换说话人:
```javascript
// 超过3秒静音,自动切换到下一个说话人
let currentSpeaker = 0;
let silenceTimer = null;

// 检测到声音
onAudioDetect(() => {
  clearTimeout(silenceTimer);
});

// 检测到静音
onSilenceDetect(() => {
  silenceTimer = setTimeout(() => {
    currentSpeaker = (currentSpeaker + 1) % 4;
  }, 3000);
});
```

### 方案2: 手动切换按钮

在界面上添加切换按钮:
```html
<div class="speaker-selector">
  <button onclick="setSpeaker(0)">👤 说话人1</button>
  <button onclick="setSpeaker(1)">👤 说话人2</button>
  <button onclick="setSpeaker(2)">👤 说话人3</button>
  <button onclick="setSpeaker(3)">👤 说话人4</button>
</div>
```

## 联系讯飞支持

如需确认功能权限:

**在线客服**: https://www.xfyun.cn/
**技术支持邮箱**: <EMAIL>
**开发者QQ群**: 搜索"讯飞开放平台"

**咨询内容**:
```
您好,我想咨询语音听写流式API的说话人分离功能:

1. 我的APPID: 4ff6d5e8
2. 当前已设置 vinfo=1 参数
3. 返回数据中没有 rl 字段
4. 请问该功能是否需要单独申请开通?
5. 是否有特殊的配置要求?

期待您的回复,谢谢!
```

---

## 下一步操作

1. **立即测试**: 重启后端,查看控制台日志
2. **确认权限**: 登录讯飞控制台检查功能开通状态
3. **根据结果**:
   - 有`rl`字段 → 检查前端显示逻辑
   - 无`rl`字段 → 联系讯飞申请开通
   - 确认无法开通 → 使用替代方案

---

*创建时间: 2025-11-17*
