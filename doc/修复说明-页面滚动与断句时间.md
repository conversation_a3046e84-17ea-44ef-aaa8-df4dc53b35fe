# 修复说明 - 页面滚动与断句时间

## 修复内容

### 1. 页面滚动问题 ✅

**问题描述:**
- 文字段落变多后,页面无法滚动,看不到最新的文字信息

**修复位置:**
- `before/css/voice-recognition.css`
- `before/html/voice-recognition.html`

**修复内容:**
1. **CSS布局优化**:
   - 为`body`添加`height: 100vh`和`margin: 0; padding: 0`,确保占满视口
   - 为`#app`容器添加`overflow: hidden`,防止外层滚动
   - 为`.document-area`添加`overflow-x: hidden`和`min-height: 0`,确保只有内容区域滚动

2. **HTML结构完善**:
   - 为`#app`元素添加`class="pro-interface"`,确保CSS选择器生效

**验证方法:**
1. 打开语音转写页面
2. 开始录音并说话,让文字内容超过一屏
3. 检查页面右侧是否出现滚动条
4. 尝试滚动,确认能看到最新消息

---

### 2. 断句时间延长 ✅

**问题描述:**
- 说话时稍微停顿0.5秒就会断句,速度太快,希望延长到1秒

**修复位置:**
- `after/config/config.json`
- `after/impl/voice_rec.py`

**修复内容:**
1. **配置文件新增参数** (`config.json`):
```json
{
  "ai_providers": {
    "xfyun": {
      "models": {
        "voice_recognition": {
          "vad_eos": 1000  // 静音断句时间(毫秒),1000=1秒
        }
      }
    }
  }
}
```

2. **后端代码实现** (`voice_rec.py`):
   - 在`__init__`方法中读取`vad_eos`配置(默认1000ms)
   - 在`create_url`方法中将`vad_eos`参数添加到WebSocket连接URL中
   - 在初始化日志中显示当前断句时长配置

**技术说明:**
- `vad_eos` (Voice Activity Detection - End Of Speech): 静音检测时长
- 单位: 毫秒 (ms)
- 取值范围: 通常300-3000ms
- 当前设置: 1000ms (1秒)
- 含义: 当检测到静音持续1秒后,认为当前句子结束,开始断句

**验证方法:**
1. 重启后端服务 (`python after/main.py`)
2. 观察启动日志,应该看到:
   ```
   ✅ 讯飞实时转写+大模型服务初始化成功
   📍 APPID: 4ff6d5e8
   🎤 说话人分离模式: role_type=2 (盲分)
   ⏱️ 断句静音时长: 1000ms  ← 确认此行
   🌐 API地址: wss://...
   ```
3. 打开语音转写页面开始录音
4. 说话时故意停顿0.5-0.8秒(短于1秒)
5. 确认不会立即断句,只有停顿超过1秒才断句

---

## 修改文件清单

| 文件路径 | 修改类型 | 说明 |
|---------|---------|------|
| `before/css/voice-recognition.css` | 修改 | 优化页面布局,确保滚动正常 |
| `before/html/voice-recognition.html` | 修改 | 添加`pro-interface`类名 |
| `after/config/config.json` | 新增配置项 | 添加`vad_eos: 1000` |
| `after/impl/voice_rec.py` | 功能增强 | 支持`vad_eos`参数配置 |

---

## 注意事项

1. **断句时长调整建议**:
   - 如果觉得1秒还是太快,可以在`config.json`中调整`vad_eos`为`1500`(1.5秒)或`2000`(2秒)
   - 修改后需要重启后端服务
   - 不建议超过3秒,可能影响实时性

2. **页面滚动优化**:
   - 已自动滚动到底部(之前已实现的`scrollToBottom`功能)
   - 如果需要手动查看历史消息,可以向上滚动
   - 新消息到来时会自动滚动到底部

3. **浏览器兼容性**:
   - 已测试Chrome/Edge最新版本
   - 滚动条样式使用`::-webkit-scrollbar`,在Firefox中为默认样式

---

## 测试场景

### 场景1: 连续说话不断句
1. 开始录音
2. 连续说: "今天天气很好(停0.5秒)我们去公园散步吧(停0.8秒)顺便买点水果"
3. **预期结果**: 整段话应该作为一个句子,不会在0.5秒或0.8秒处断句

### 场景2: 长时间停顿断句
1. 开始录音
2. 说: "第一句话"(停顿1.2秒)"第二句话"
3. **预期结果**: 在1.2秒停顿后断句,分为两个句子

### 场景3: 页面滚动
1. 开始录音
2. 连续说话10-15个句子,使内容超过一屏
3. **预期结果**: 
   - 右侧出现滚动条
   - 自动滚动到最新消息
   - 可手动向上滚动查看历史

---

## 回滚方案

如果修改后出现问题,可以回滚:

### 回滚断句时间:
```json
// config.json 中删除或注释掉 vad_eos 行
"vad_eos": 1000  // 删除此行,使用讯飞默认值(约500ms)
```

### 回滚页面滚动:
```bash
git checkout before/css/voice-recognition.css
git checkout before/html/voice-recognition.html
```

---

**修复完成时间**: 2025-11-21  
**修复人**: AI Assistant
