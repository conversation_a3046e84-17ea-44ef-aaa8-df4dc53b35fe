# 配置文件结构图

## 📊 完整结构树

```
config.json
│
├─ ai_providers (AI服务提供商)
│  │
│  ├─ volcengine (火山引擎) ✅ 已配置
│  │  ├─ api_key: "480b4cd8-2d62-..."
│  │  ├─ base_url: "https://ark.cn-beijing.volces.com/api/v3"
│  │  ├─ timeout: 60
│  │  └─ models (模型配置)
│  │     └─ image_generation (图片生成)
│  │        ├─ model_name: "doubao-seedream-4-0-250828"
│  │        ├─ model_id: "doubao-seedream-4-0-250828"
│  │        ├─ max_resolution: "2560x2560"
│  │        └─ preset_sizes: [...]
│  │
│  └─ openai (OpenAI) ⏳ 待配置
│     ├─ api_key: "YOUR_OPENAI_API_KEY_HERE"
│     ├─ base_url: "https://api.openai.com/v1"
│     ├─ timeout: 60
│     └─ models
│        └─ image_generation
│           ├─ model_name: "dall-e-3"
│           ├─ model_id: "dall-e-3"
│           └─ max_resolution: "1024x1024"
│
├─ active_providers (当前激活的提供商)
│  ├─ image_generation: "volcengine" ✅
│  ├─ video_generation: null
│  ├─ voice_recognition: null
│  └─ quality_check: null
│
├─ database (数据库配置)
│  ├─ host: "localhost"
│  ├─ port: 5432
│  ├─ username: "your_username"
│  ├─ password: "your_password"
│  └─ database: "ai_tools_db"
│
└─ server (服务器配置)
   ├─ host: "127.0.0.1"
   ├─ port: 8000
   ├─ debug: false
   └─ cors_origins: ["*"]
```

---

## 🔄 数据流向

```
用户请求
   ↓
API接口 (/api/image-generation)
   ↓
读取 active_providers.image_generation
   ↓
获取提供商名称: "volcengine"
   ↓
加载 ai_providers.volcengine
   ├─ api_key
   ├─ base_url
   └─ models.image_generation
       ├─ model_id
       └─ 其他配置
   ↓
调用火山引擎API
   ↓
返回结果
```

---

## 🎯 提供商维度设计

### 优势展示

```
┌─────────────────────────────────────┐
│        AI Providers (提供商)         │
├─────────────────────────────────────┤
│                                     │
│  ┌─────────────┐  ┌─────────────┐  │
│  │ Volcengine  │  │   OpenAI    │  │
│  ├─────────────┤  ├─────────────┤  │
│  │ • API Key   │  │ • API Key   │  │
│  │ • Base URL  │  │ • Base URL  │  │
│  │ • Models    │  │ • Models    │  │
│  │   - Image   │  │   - Image   │  │
│  │   - Video   │  │   - Text    │  │
│  │   - Voice   │  │   - Voice   │  │
│  └─────────────┘  └─────────────┘  │
│                                     │
│  ┌─────────────┐  ┌─────────────┐  │
│  │ Anthropic   │  │  Midjourney │  │
│  │  (待添加)    │  │   (待添加)   │  │
│  └─────────────┘  └─────────────┘  │
│                                     │
└─────────────────────────────────────┘
            ↓
┌─────────────────────────────────────┐
│     Active Providers (激活配置)      │
├─────────────────────────────────────┤
│ image_generation → volcengine  ✅   │
│ video_generation → null        ⏳   │
│ voice_recognition → null       ⏳   │
│ quality_check → null           ⏳   │
└─────────────────────────────────────┘
```

---

## 🔀 切换提供商流程

### 步骤1: 添加新提供商配置

```json
{
  "ai_providers": {
    "new_provider": {
      "api_key": "xxx",
      "base_url": "https://api.example.com",
      "models": {...}
    }
  }
}
```

### 步骤2: 激活新提供商

```json
{
  "active_providers": {
    "image_generation": "new_provider"
  }
}
```

### 步骤3: 重启服务

```powershell
python main.py
```

### 流程图

```
┌──────────────┐
│ 添加提供商配置 │
└──────┬───────┘
       │
       ↓
┌──────────────┐
│ 修改激活配置  │
└──────┬───────┘
       │
       ↓
┌──────────────┐
│   重启服务    │
└──────┬───────┘
       │
       ↓
┌──────────────┐
│  自动适配 ✅  │
└──────────────┘
```

---

## 📋 配置层级关系

```
Level 1: 顶层分类
├─ ai_providers      (提供商配置)
├─ active_providers  (激活配置)
├─ database         (数据库)
└─ server           (服务器)

Level 2: 提供商详情
ai_providers
├─ volcengine
├─ openai
└─ ...

Level 3: 提供商属性
volcengine
├─ api_key
├─ base_url
├─ timeout
└─ models

Level 4: 模型配置
models
├─ image_generation
├─ video_generation
└─ voice_recognition

Level 5: 模型详情
image_generation
├─ model_name
├─ model_id
├─ max_resolution
└─ preset_sizes
```

---

## 🎨 配置示例对比

### 单提供商 (当前)

```json
{
  "ai_providers": {
    "volcengine": {...}
  },
  "active_providers": {
    "image_generation": "volcengine"
  }
}
```

### 多提供商 (未来)

```json
{
  "ai_providers": {
    "volcengine": {...},
    "openai": {...},
    "anthropic": {...}
  },
  "active_providers": {
    "image_generation": "volcengine",
    "text_generation": "openai",
    "chat": "anthropic"
  }
}
```

---

## 🔐 安全性设计

```
┌─────────────────────────────────┐
│       敏感信息集中管理           │
├─────────────────────────────────┤
│                                 │
│  ai_providers.*.api_key  🔒     │
│  database.password       🔒     │
│                                 │
│  ↓ 建议                          │
│  • 使用环境变量               │
│  • 加密存储                   │
│  • 不提交到Git               │
│                                 │
└─────────────────────────────────┘
```

---

## 📈 扩展性设计

### 当前支持

- ✅ 火山引擎 (图片生成)
- ⏳ OpenAI (预留配置)

### 未来扩展

```
ai_providers (可扩展)
├─ volcengine
│  └─ models
│     ├─ image_generation ✅
│     ├─ video_generation ⏳
│     └─ voice_recognition ⏳
│
├─ openai
│  └─ models
│     ├─ image_generation (DALL-E)
│     ├─ text_generation (GPT-4)
│     └─ embedding
│
├─ anthropic
│  └─ models
│     └─ chat (Claude)
│
└─ midjourney
   └─ models
      └─ image_generation
```

---

## 🎯 最佳实践

### ✅ 推荐

```json
{
  "ai_providers": {
    "provider_name": {
      "api_key": "env:PROVIDER_API_KEY",  // 使用环境变量
      "timeout": 60,
      "models": {
        "feature": {
          "model_id": "specific-model-version"  // 明确版本
        }
      }
    }
  }
}
```

### ❌ 不推荐

```json
{
  "api_key": "hardcoded-key-in-json",  // 明文存储
  "model": "latest"  // 版本不明确
}
```

---

**配置结构清晰、易扩展、易维护!** 🎉
