# 语音识别快速开始

## 一分钟快速测试

### 步骤1: 启动服务

**后端服务** (新建命令行窗口):
```bash
cd d:\private\private01\after
python main.py
```

看到以下输出表示成功:
```
✅ 讯飞语音识别服务初始化成功 (APPID: 4ff6d5e8)
INFO:     Uvicorn running on http://127.0.0.1:8000
```

**前端服务** (双击运行):
```
d:\private\private01\启动项目.bat
```

自动打开浏览器访问: `http://localhost:3000/html/`

### 步骤2: 访问语音识别页面

在主页点击 **"🎤 声音识别"** 卡片

或直接访问: `http://localhost:3000/html/voice-recognition.html`

### 步骤3: 开始录音

1. 点击 **"🎙️ 开始录音"** 按钮
2. 浏览器弹出权限请求,点击 **"允许"**
3. 状态指示器变为红色 "录音中..."
4. 开始说话,文字实时显示在页面上

### 步骤4: 测试说话人盲分

- 让不同的人轮流说话
- 观察识别结果显示不同颜色的边框
- 紫色、粉色、绿色、黄色代表不同说话人

### 步骤5: 停止录音

点击 **"⏹️ 停止录音"** 按钮

## 页面功能说明

### 控制按钮
- **开始录音**: 连接服务并开启麦克风
- **停止录音**: 停止录音并关闭连接  
- **清空记录**: 清除所有识别结果

### 状态指示
- **灰色**: 未连接
- **绿色脉冲**: 已连接
- **红色脉冲**: 录音中

### 音量可视化
实时显示音量波形,确认麦克风正常工作

### 识别结果区
- 显示识别的文字内容
- 说话人标签 (说话人1/2/3/4)
- 时间戳
- 自动滚动到最新内容

## 测试建议

### 测试场景1: 单人录音
说一段话,检查识别准确率:
> "你好,这是语音识别测试。今天天气很好,适合出去走走。"

### 测试场景2: 多人对话
两个人轮流说话:
> A: "你好,最近怎么样?"  
> B: "挺好的,你呢?"  
> A: "我也不错,周末有空吗?"

观察是否能正确区分说话人

### 测试场景3: 会议场景
模拟会议讨论,3-4人轮流发言

## 常见测试问题

### 问题1: 没有声音波形
**解决**: 检查麦克风是否正确连接,系统音量是否静音

### 问题2: 识别结果为空
**解决**: 
- 确认后端服务正常运行
- 查看浏览器控制台是否有错误
- 检查网络连接

### 问题3: 识别结果不准确
**解决**:
- 尽量清晰发音
- 在安静环境测试
- 靠近麦克风说话

### 问题4: 说话人识别混乱
**解决**:
- 确保不同说话人声音特征明显
- 避免声音重叠
- 说话间隔1-2秒

## 技术验证点

### ✅ 前端功能
- [ ] 麦克风权限获取成功
- [ ] WebSocket连接成功
- [ ] 音频数据采集正常
- [ ] 音量可视化显示
- [ ] 实时显示识别结果

### ✅ 后端功能  
- [ ] FastAPI服务启动成功
- [ ] WebSocket端点可访问
- [ ] 讯飞API鉴权成功
- [ ] 实时转写返回结果
- [ ] 说话人ID正确解析

### ✅ 端到端流程
- [ ] 音频采集 → PCM转换 → Base64编码
- [ ] WebSocket发送 → 后端接收
- [ ] 讯飞识别 → 结果解析
- [ ] WebSocket返回 → 前端显示
- [ ] 说话人盲分生效

## 下一步

测试成功后,可以:
1. 查看完整使用指南: `doc/voice-recognition-guide.md`
2. 研究实现原理: `before/js/voice-recognition.js`
3. 查看后端实现: `after/impl/voice_rec.py`
4. 自定义配置参数: `after/config/config.json`

---

**祝测试顺利!** 🎉
