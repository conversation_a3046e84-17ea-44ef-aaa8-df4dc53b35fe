# 常见问题解决方案

## 问题1: 点击生成图片报错 "Failed to fetch"

### 原因分析
此错误通常由以下原因导致:
1. **CORS跨域问题** - 后端未允许前端域名访问
2. **后端服务未启动** - API服务没有运行
3. **端口占用** - 8000端口被其他程序占用
4. **防火墙拦截** - 系统防火墙阻止了请求

### 解决方案

#### 方案1: 已修复 - CORS配置更新 ✅
已将CORS配置改为允许所有来源(开发模式):
```json
"cors_origins": ["*"]
```

#### 方案2: 确保后端服务正在运行
```powershell
# 进入后端目录
cd d:/private/private01/after

# 启动服务
python main.py
```

确认看到以下输出:
```
==================================================
AI工具导航后端服务
==================================================
服务地址: http://0.0.0.0:8000
API文档: http://0.0.0.0:8000/docs
==================================================
```

#### 方案3: 使用本地服务器打开前端(推荐)
不要直接双击 `index.html`,而是使用HTTP服务器:

```powershell
# 进入前端目录
cd d:/private/private01/before/html

# 启动HTTP服务器
python -m http.server 3000
```

然后在浏览器访问: `http://localhost:3000`

#### 方案4: 检查端口是否被占用
```powershell
# 检查8000端口
netstat -ano | findstr :8000

# 如果被占用,修改配置文件中的端口号
# 编辑: after/config/config.json
# 将 "port": 8000 改为其他端口,如 8001
```

#### 方案5: 测试API是否可访问
在浏览器中直接访问:
```
http://localhost:8000/api/health
```

如果看到 `{"status":"healthy"}`,说明后端服务正常。

---

## 问题2: 生成图片很慢或超时

### 原因
- 火山AI服务器响应慢
- 网络连接不稳定
- 提示词过于复杂

### 解决方案
1. 检查网络连接
2. 简化提示词
3. 增加超时时间(配置文件 `volcengine.timeout`)
4. 重试请求

---

## 问题3: 图片URL失效(404错误)

### 原因
火山AI返回的图片URL **仅24小时有效**

### 解决方案
1. 及时下载保存图片
2. 实施OSS永久存储(后续功能)
3. 使用数据库记录历史(后续功能)

---

## 问题4: API Key无效

### 错误信息
```
API调用失败: Invalid API Key
```

### 解决方案
1. 检查 `after/config/config.json` 中的 `api_keys.image_generation`
2. 确保API Key有效且有足够额度
3. 前往火山引擎控制台重新生成API Key

---

## 问题5: 后端启动报错 "ModuleNotFoundError"

### 错误信息
```
ModuleNotFoundError: No module named 'fastapi'
```

### 解决方案
安装依赖:
```powershell
cd d:/private/private01/after
pip install -r requirements.txt
```

---

## 问题6: 前端页面样式错乱

### 原因
- CSS文件未加载
- Vue未正确引入

### 解决方案
1. 检查网络连接(Vue使用CDN)
2. 使用浏览器开发者工具查看控制台错误
3. 确保所有CSS/JS文件路径正确

---

## 调试技巧

### 1. 查看浏览器控制台
按 `F12` 打开开发者工具,查看:
- **Console**: JavaScript错误
- **Network**: 网络请求状态
- **Response**: API返回内容

### 2. 查看后端日志
后端服务会输出详细日志,包括:
- API调用参数
- 火山AI响应
- 错误信息

### 3. 测试API接口
使用Swagger文档测试:
```
http://localhost:8000/docs
```

### 4. 使用curl测试
```powershell
curl -X POST "http://localhost:8000/api/image-generation" ^
  -H "Content-Type: application/json" ^
  -d "{\"prompt\":\"一只猫\",\"width\":2048,\"height\":2048}"
```

---

## 联系支持

如果以上方案都无法解决问题:
1. 查看完整错误日志
2. 检查网络连接
3. 确认API Key有效
4. 提供详细错误信息以便排查
