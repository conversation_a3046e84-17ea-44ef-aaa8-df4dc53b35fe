# 图片生成功能说明

## 功能概述

本功能已成功集成火山引擎豆包AI图片生成能力,支持:

- ✅ **文生图**: 根据文字描述生成图片
- ✅ **多种尺寸**: 支持5种预设比例(1:1, 4:3, 3:4, 16:9, 9:16)
- ✅ **随机种子**: 支持固定种子以重现相同图片
- ✅ **高质量输出**: 最高支持2560x2560分辨率
- 🔄 **图生图**: 预留接口,需实现图片上传功能后启用

## 技术实现

### 后端实现 (Python FastAPI)

**核心文件:**
- `after/impl/image_gen.py` - 图片生成核心逻辑
- `after/main.py` - API接口定义
- `after/config/config.json` - 配置文件

**API端点:**
```
POST /api/image-generation
GET  /api/image-generation/preset-sizes
```

**请求示例:**
```json
{
  "prompt": "一只可爱的橙色小猫坐在花园里",
  "width": 2048,
  "height": 2048,
  "seed": 12345,
  "watermark": false
}
```

**响应示例:**
```json
{
  "success": true,
  "message": "图片生成成功",
  "data": {
    "image_url": "https://tos-cn-beijing.volces.com/xxx.jpg",
    "prompt": "一只可爱的橙色小猫坐在花园里",
    "width": 2048,
    "height": 2048,
    "seed": 12345,
    "duration_ms": 8234
  }
}
```

### 前端实现 (Vue 3 CDN)

**核心文件:**
- `before/html/image-generation.html` - 主页面
- `before/js/image-generation.js` - Vue组件
- `before/css/image-generation.css` - 样式

**页面布局:**
```
┌─────────────────────────────────────────────┐
│  顶部导航栏                                  │
├──────────────────┬──────────────────────────┤
│                  │  右侧控制面板             │
│  左侧预览区      │  ┌────────────────────┐ │
│  (瀑布流)        │  │ 提示词输入框       │ │
│                  │  │ 参考图片上传       │ │
│  ┌────┐ ┌────┐  │  │ 图片比例选择       │ │
│  │图片│ │图片│  │  │ 随机种子           │ │
│  └────┘ └────┘  │  └────────────────────┘ │
│                  │  [生成图片按钮]         │
└──────────────────┴──────────────────────────┘
```

## 火山引擎配置

### API密钥
```
480b4cd8-2d62-423d-8a59-bb6e47cf3776
```

### 模型信息
- **模型名称**: doubao-seedream-4-0-250828
- **基础URL**: https://ark.cn-beijing.volces.com/api/v3
- **超时时间**: 60秒

### 尺寸规范

**文生图模式:**
- 格式: `宽x高` (如 `2048x2048`)
- 最小像素: 921600

**图生图模式:**
- 格式: `2K` 或 `4K`
- 2K: 像素数 < 4194304
- 4K: 像素数 ≥ 4194304

## 使用指南

### 1. 启动后端服务

```bash
# 安装依赖
cd d:/private/private01/after
pip install -r requirements.txt

# 启动服务
python main.py
```

服务地址: http://localhost:8000

### 2. 打开前端页面

直接打开浏览器访问:
```
d:/private/private01/before/html/index.html
```

点击"图片生成"卡片即可进入生成页面。

### 3. 生成图片

1. 输入提示词(必填)
2. 选择图片比例(可选,默认1:1)
3. 设置随机种子(可选,留空随机)
4. 点击"生成图片"按钮
5. 等待10-30秒,图片会显示在左侧预览区

### 4. 下载图片

点击图片下方的"下载"按钮即可保存到本地。

## 注意事项

### 图片URL有效期
火山引擎返回的图片URL **仅有24小时有效期**,超过时间后将无法访问。

**解决方案:**
- 生成后立即下载保存
- 或实现OSS存储功能(参考原文档)

### 参考图片上传
当前版本**暂不支持参考图片上传**功能,原因:
- 需要实现文件上传到服务器的接口
- 需要配置OSS或本地存储
- 火山AI要求参考图片必须是公网可访问的URL

**如需启用图生图:**
1. 实现图片上传接口
2. 获取图片公网URL
3. 在前端发送请求时包含 `reference_images` 字段

### API调用限制
- 每次请求超时: 60秒
- 建议添加请求频率限制
- 如遇429错误,说明请求过于频繁,需等待后重试

## 错误处理

### 常见错误

**1. 提示词为空**
```
错误: 提示词不能为空
解决: 输入有效的提示词
```

**2. API调用失败**
```
错误: API调用失败: invalid api key
解决: 检查配置文件中的API密钥是否正确
```

**3. 网络超时**
```
错误: 请求超时,超过60秒
解决: 检查网络连接,或增加timeout配置
```

**4. CORS错误**
```
错误: Access to fetch blocked by CORS policy
解决: 确保后端已启动,或配置CORS允许的来源
```

## 性能优化建议

1. **并发控制**: 限制同时生成的数量
2. **缓存机制**: 相同参数可复用结果
3. **图片压缩**: 下载时可选择压缩质量
4. **OSS存储**: 永久保存生成的图片

## 后续扩展

### 待实现功能
- [ ] 图生图(参考图片上传)
- [ ] 历史记录保存(数据库)
- [ ] 批量生成
- [ ] 图片编辑(裁剪、调整)
- [ ] OSS永久存储
- [ ] 用户管理和鉴权

### 数据库表设计参考

如需保存历史记录,可参考以下表结构:

```sql
CREATE TABLE ai_images (
  id INT AUTO_INCREMENT PRIMARY KEY,
  prompt TEXT NOT NULL,
  width INT NOT NULL,
  height INT NOT NULL,
  seed INT,
  image_url TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_created (created_at)
);
```

## 技术支持

- 火山引擎文档: https://www.volcengine.com/docs/82379/1263512
- FastAPI文档: https://fastapi.tiangolo.com/
- Vue 3文档: https://vuejs.org/

## 更新日志

### v1.0.0 (2025-11-17)
- ✅ 完成火山AI图片生成功能对接
- ✅ 实现文生图功能
- ✅ 支持多种预设尺寸
- ✅ 支持随机种子
- ✅ 实现图片下载功能
- 🔄 预留图生图接口
