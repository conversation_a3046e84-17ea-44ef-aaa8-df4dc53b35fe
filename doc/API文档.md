# API接口文档

## 基础信息

- **Base URL**: `http://localhost:8000`
- **数据格式**: JSON
- **编码**: UTF-8

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": { ... }
}
```

### 失败响应
```json
{
  "success": false,
  "message": "错误描述",
  "data": null
}
```

## 接口列表

### 1. 根路径
**GET** `/`

获取API基本信息和可用端点列表。

**响应示例**:
```json
{
  "message": "AI工具导航API服务",
  "version": "1.0.0",
  "endpoints": {
    "image_generation": "/api/image-generation",
    "video_generation": "/api/video-generation",
    "voice_recognition": "/api/voice-recognition",
    "quality_check": "/api/quality-check"
  }
}
```

---

### 2. 图片生成
**POST** `/api/image-generation`

根据文字描述生成图片。

**请求体**:
```json
{
  "prompt": "一只可爱的猫咪在星空下",
  "style": "realistic"
}
```

**参数说明**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| prompt | string | 是 | 图片描述文字，最多1000字符 |
| style | string | 否 | 图片风格，默认"realistic" |

**响应示例**:
```json
{
  "success": false,
  "message": "AI接口尚未对接",
  "data": {
    "prompt": "一只可爱的猫咪在星空下",
    "image_url": null,
    "parameters": {
      "style": "realistic"
    }
  }
}
```

**错误码**:
- `400`: 提示词为空或过长

---

### 3. 视频生成
**POST** `/api/video-generation`

根据文字或图片生成视频。

**请求体**:
```json
{
  "prompt": "未来城市的日出",
  "duration": 5
}
```

**参数说明**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| prompt | string | 是 | 视频描述文字或图片 |
| duration | integer | 否 | 视频时长(秒)，默认5，最大10 |

**响应示例**:
```json
{
  "success": false,
  "message": "AI接口尚未对接",
  "data": {
    "prompt": "未来城市的日出",
    "duration": 5,
    "video_url": null,
    "task_id": null
  }
}
```

**错误码**:
- `400`: 视频时长超过限制

---

### 4. 视频任务状态查询
**GET** `/api/video-generation/status/{task_id}`

查询视频生成任务的状态。

**路径参数**:
| 参数 | 类型 | 说明 |
|------|------|------|
| task_id | string | 任务ID |

**响应示例**:
```json
{
  "task_id": "abc123",
  "status": "processing",
  "progress": 45,
  "video_url": null
}
```

**状态说明**:
- `pending`: 等待处理
- `processing`: 处理中
- `completed`: 已完成
- `failed`: 失败

---

### 5. 语音识别
**POST** `/api/voice-recognition`

将语音转换为文字。

**请求体**:
```json
{
  "audio_data": "base64编码的音频数据",
  "language": "zh"
}
```

**参数说明**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| audio_data | string | 是 | base64编码的音频数据或文件路径 |
| language | string | 否 | 语言代码，默认"zh"，支持zh/en |

**响应示例**:
```json
{
  "success": false,
  "message": "AI接口尚未对接",
  "data": {
    "text": null,
    "language": "zh",
    "confidence": 0,
    "segments": []
  }
}
```

**错误码**:
- `400`: 不支持的语言

---

### 6. 代码质量检查
**POST** `/api/quality-check`

分析代码质量并提供优化建议。

**请求体**:
```json
{
  "code": "def hello():\n    print('Hello, World!')",
  "language": "python"
}
```

**参数说明**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| code | string | 是 | 待分析的代码 |
| language | string | 否 | 编程语言，默认"python"，支持python/javascript/java |

**响应示例**:
```json
{
  "success": false,
  "message": "AI接口尚未对接",
  "data": {
    "score": 0,
    "issues": [],
    "suggestions": [],
    "metrics": {
      "complexity": 0,
      "maintainability": 0,
      "security": 0,
      "performance": 0
    }
  }
}
```

**错误码**:
- `400`: 不支持的编程语言

---

### 7. 健康检查
**GET** `/api/health`

检查服务健康状态。

**响应示例**:
```json
{
  "status": "healthy"
}
```

---

## 错误处理

所有接口在发生错误时会返回相应的HTTP状态码：

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 使用示例

### Python
```python
import requests

# 图片生成
response = requests.post(
    "http://localhost:8000/api/image-generation",
    json={
        "prompt": "一只可爱的猫咪",
        "style": "realistic"
    }
)
print(response.json())
```

### JavaScript
```javascript
// 图片生成
fetch('http://localhost:8000/api/image-generation', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    prompt: '一只可爱的猫咪',
    style: 'realistic'
  })
})
.then(response => response.json())
.then(data => console.log(data));
```

## 注意事项

1. 所有POST请求需设置 `Content-Type: application/json`
2. 当前所有AI接口均为占位符，返回"AI接口尚未对接"
3. 实际对接AI服务后，响应格式保持一致
4. 建议在生产环境启用HTTPS
5. API密钥应在配置文件中妥善保管

## 交互式文档

启动服务后访问 http://localhost:8000/docs 可以查看Swagger自动生成的交互式API文档。
