# 火山AI图片生成功能集成文档

> **文档版本**: v1.0  
> **更新时间**: 2025-11-17  
> **适用场景**: 将火山引擎豆包AI图片生成能力集成到项目中

---

## 📋 目录

1. [功能概述](#功能概述)
2. [技术架构](#技术架构)
3. [环境配置](#环境配置)
4. [数据库设计](#数据库设计)
5. [后端实现](#后端实现)
6. [前端实现](#前端实现)
7. [火山AI对接](#火山ai对接)
8. [完整代码示例](#完整代码示例)
9. [常见问题](#常见问题)

---

## 功能概述

### 核心功能

本系统实现了基于火山引擎豆包AI的智能图片生成功能，支持：

- ✅ **文生图**: 根据文字描述生成图片
- ✅ **图生图**: 基于参考图片生成新图片（支持多图，最多4张）
- ✅ **历史记录**: 保存和查询生成历史
- ✅ **图片管理**: 下载、删除、预览生成的图片
- ✅ **OSS存储**: 图片永久保存到阿里云OSS

### 业务流程

```
用户输入提示词 
    ↓
（可选）上传参考图片到OSS
    ↓
调用火山AI生成图片（临时URL）
    ↓
下载图片并上传到OSS（永久URL）
    ↓
保存记录到数据库
    ↓
展示生成结果
```

---

## 技术架构

### 技术栈

**前端:**
- Vue 3 (Composition API)
- Vite
- TailwindCSS
- Axios

**后端:**
- Node.js + Express
- MySQL (数据库)
- Ali-OSS (对象存储)
- Axios (HTTP请求)

**第三方服务:**
- 火山引擎豆包AI (图片生成)
- 阿里云OSS (图片存储)

### 架构图

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│   前端UI    │ HTTP │  后端API    │ HTTP │  火山AI     │
│  Vue 3      │─────>│  Express    │─────>│  豆包模型   │
└─────────────┘      └─────────────┘      └─────────────┘
                            │
                            ├─────> MySQL (保存记录)
                            │
                            └─────> 阿里云OSS (图片存储)
```

---

## 环境配置

### 1. 火山引擎配置

#### API密钥获取
火山密钥：480b4cd8-2d62-423d-8a59-bb6e47cf3776

#### 重要配置参数

```javascript
// 火山引擎配置
const volcConfig = {
  apiKey: '480b4cd8-2d62-423d-8a59-bb6e47cf3776',  // 
  baseUrl: 'https://ark.cn-beijing.volces.com/api/v3',
  model: 'doubao-seedream-4-0-250828'  // 豆包SeedreamV4模型
}
```

### 2. 阿里云OSS配置


#### OSS配置参数
```javascript
// 阿里云OSS配置
const ossConfig = {
  region: 'oss-cn-szfinance',
  accessKeyId: 'LTAI5t9cVPASvgKFkt1DsiZT',      // 
  accessKeySecret: '******************************',  // 
  bucket: 'hq-prd-e-zine',                      // 
  endpoint: 'oss-cn-szfinance.aliyuncs.com'
}

// 存储路径规范
const ossPaths = {
  referenceImages: '/agent/uat/ai-reference-images/',  // 参考图片路径
  generatedImages: '/agent/uat/ai-generated-images/'   // 生成图片路径
}
```

### 3. 数据库配置

```javascript
// MySQL数据库配置
const dbConfig = {
  host: 'rm-j5eqoq656ktq48882.mysql.rds.aliyuncs.com:3306/hqins_agent_sales_dat?useSSL=false',  // 
  port: 3306,
  user: 'sales_dat',        // 
  password: 'Hqins@66',          // 
  connectionLimit: 10
}
```

### 4. 环境变量文件 (.env)

创建 `backend/.env` 文件：

```bash
# 数据库配置
DB_HOST=rm-j5eqoq656ktq48882.mysql.rds.aliyuncs.com:3306/hqins_agent_sales_dat?useSSL=false
DB_PORT=3306
DB_USER=sales_dat
DB_PASSWORD=Hqins@66
DB_NAME=sales_dat

# 火山引擎配置
VOLC_API_KEY=480b4cd8-2d62-423d-8a59-bb6e47cf3776
VOLC_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
VOLC_MODEL=doubao-seedream-4-0-250828

# 服务端口
PORT=3001
```

---

## 数据库设计

### 表结构: `private_ai_images`

用于存储AI图片生成的请求和响应记录。

```sql
CREATE TABLE `private_ai_images` (
  `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  
  -- 请求参数
  `request_prompt` TEXT COMMENT '用户输入的提示词',
  `request_width` INT(11) COMMENT '请求的图片宽度',
  `request_height` INT(11) COMMENT '请求的图片高度',
  `request_size` VARCHAR(20) COMMENT '请求的尺寸规格(如2K/4K/1024x1024)',
  `request_seed` BIGINT COMMENT '随机种子值',
  `request_watermark` TINYINT(1) DEFAULT 0 COMMENT '是否添加水印(0:否,1:是)',
  `request_guidance_scale` DECIMAL(5,2) COMMENT '引导系数',
  `request_body` TEXT COMMENT '完整的请求JSON',
  
  -- 响应数据
  `response_status` VARCHAR(20) DEFAULT 'pending' COMMENT '响应状态(pending/success/failed)',
  `response_image_url` TEXT COMMENT '火山返回的临时图片URL(24小时有效)',
  `response_model` VARCHAR(100) COMMENT '使用的模型名称',
  `response_created` BIGINT COMMENT '火山返回的创建时间戳',
  `response_body` TEXT COMMENT '完整的响应JSON',
  `response_error` TEXT COMMENT '错误信息(如果失败)',
  
  -- 图片信息
  `image_url` TEXT COMMENT '永久保存的OSS图片URL',
  `image_width` INT(11) COMMENT '实际生成的图片宽度',
  `image_height` INT(11) COMMENT '实际生成的图片高度',
  `image_pixels` BIGINT COMMENT '图片总像素数',
  
  -- 性能指标
  `api_duration_ms` INT(11) COMMENT 'API调用耗时(毫秒)',
  
  -- 通用字段
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` TINYINT(1) DEFAULT 0 COMMENT '是否删除(0:否,1:是)',
  `deleted_at` TIMESTAMP NULL COMMENT '删除时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_status` (`response_status`),
  KEY `idx_created` (`created_at`),
  KEY `idx_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI图片生成记录表';
```

---

## 后端实现

### 1. 参考图片上传API

**端点:** `POST /api/poster/upload-reference-image`

**功能:** 上传参考图片到OSS，返回公网URL

```javascript
const multer = require('multer')
const OSS = require('ali-oss')

// 配置multer (内存存储)
const upload = multer({
  storage: multer.memoryStorage(),
  limits: { fileSize: 5 * 1024 * 1024 } // 5MB限制
})

app.post('/api/poster/upload-reference-image', upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: '400000',
        message: '未上传图片文件'
      })
    }

    // 验证文件类型
    const allowedMimes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedMimes.includes(req.file.mimetype)) {
      return res.status(400).json({
        code: '400001',
        message: '仅支持 JPG、PNG、WEBP 格式图片'
      })
    }

    // 初始化OSS客户端
    const ossClient = new OSS({
      region: 'oss-cn-szfinance',
      accessKeyId: 'LTAI5t9cVPASvgKFkt1DsiZT',
      accessKeySecret: '******************************',
      bucket: 'hq-prd-e-zine',
      endpoint: 'oss-cn-szfinance.aliyuncs.com'
    })

    // 生成唯一文件名
    const timestamp = Date.now()
    const randomStr = Math.random().toString(36).substring(2, 8)
    const ext = req.file.originalname.split('.').pop() || 'jpg'
    const ossPath = `/agent/uat/ai-reference-images/${timestamp}-${randomStr}.${ext}`

    // 上传到OSS
    const result = await ossClient.put(ossPath, req.file.buffer, {
      headers: { 'Content-Type': req.file.mimetype }
    })

    console.log('参考图片上传OSS成功:', result.url)

    res.json({
      code: '000000',
      message: '图片上传成功',
      data: {
        url: result.url,  // 返回OSS公网URL
        size: req.file.size,
        mimetype: req.file.mimetype,
        ossPath: ossPath
      }
    })
  } catch (error) {
    console.error('图片上传失败:', error)
    res.status(500).json({
      code: '500000',
      message: '图片上传失败: ' + error.message
    })
  }
})
```

### 2. 图片生成API

**端点:** `POST /api/poster/generate-image`

**功能:** 调用火山AI生成图片，并保存到OSS和数据库

**请求体:**
```json
{
  "prompt": "一只可爱的橙色小猫坐在花园里",
  "width": 2048,
  "height": 2048,
  "seed": 12345,
  "images": [
    "https://oss.example.com/image1.jpg",
    "https://oss.example.com/image2.jpg"
  ],
  "watermark": false,
  "guidance_scale": 7.5
}
```

**核心代码:**

```javascript
app.post('/api/poster/generate-image', async (req, res) => {
  const startTime = Date.now()
  let recordId = null
  
  try {
    const { 
      prompt, 
      width = 1024, 
      height = 1024,
      seed,
      image,      // 单张参考图片URL(兼容旧版)
      images,     // 多张参考图片URL数组(新版)
      watermark = false,
      guidance_scale
    } = req.body

    // 参数验证
    if (!prompt || typeof prompt !== 'string' || prompt.trim() === '') {
      return res.status(400).json({
        code: '400000',
        message: '提示词不能为空'
      })
    }

    // 判断是否为图生图模式
    const referenceImages = images || (image ? [image] : [])
    const isImageToImage = referenceImages.length > 0

    console.log('开始调用火山引擎生成图片...')
    console.log('模式:', isImageToImage ? `图生图(${referenceImages.length}张参考图)` : '文生图')
    console.log('提示词:', prompt)
    console.log('尺寸:', `${width}x${height}`)

    // 火山引擎API配置
    const volcApiKey = process.env.VOLC_API_KEY || '480b4cd8-2d62-423d-8a59-bb6e47cf3776'
    const volcBaseUrl = process.env.VOLC_BASE_URL || 'https://ark.cn-beijing.volces.com/api/v3'
    const volcModel = process.env.VOLC_MODEL || 'doubao-seedream-4-0-250828'

    // 构建尺寸参数
    let imageSize
    if (isImageToImage) {
      // 图生图模式: 使用 2K/4K 格式
      const pixels = width * height
      imageSize = pixels >= 4194304 ? '4K' : '2K'
    } else {
      // 文生图模式: 使用精确尺寸
      imageSize = `${width}x${height}`
    }

    // 构建请求体
    const axios = require('axios')
    const requestBody = {
      model: volcModel,
      prompt: prompt,
      size: imageSize,
      response_format: 'url',
      watermark: watermark,
      sequential_image_generation: 'disabled'  // 关闭组图功能
    }
    
    // 图生图模式: 添加参考图片
    if (isImageToImage) {
      if (referenceImages.length === 1) {
        // 单图模式: 使用image字段(字符串)
        requestBody.image = referenceImages[0]
      } else {
        // 多图模式: 使用image字段(数组) ⚠️ 关键代码
        requestBody.image = referenceImages
      }
    }
    
    // 添加可选参数 - seed
    if (seed !== undefined && seed !== null && seed !== '' && seed !== -1) {
      const seedValue = parseInt(seed)
      if (!isNaN(seedValue) && seedValue >= 0 && seedValue <= 2147483647) {
        requestBody.seed = seedValue
      }
    }
    
    // 添加可选参数 - guidance_scale
    if (guidance_scale !== undefined && guidance_scale !== null) {
      const scaleValue = parseFloat(guidance_scale)
      if (!isNaN(scaleValue)) {
        requestBody.guidance_scale = scaleValue
      }
    }
    
    console.log('发送给火山引擎的完整请求体:', JSON.stringify(requestBody, null, 2))
    
    // ===== 步骤1: 保存请求记录到数据库 =====
    try {
      const insertSql = `
        INSERT INTO private_ai_images 
        (request_prompt, request_width, request_height, request_size, 
         request_seed, request_watermark, request_guidance_scale, request_body,
         response_status, image_width, image_height, image_pixels)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending', ?, ?, ?)
      `
      const insertParams = [
        prompt,
        width,
        height,
        imageSize,
        requestBody.seed || null,
        watermark ? 1 : 0,
        requestBody.guidance_scale || null,
        JSON.stringify(requestBody),
        width,
        height,
        width * height
      ]
      
      const insertResult = await executeQuery(insertSql, insertParams)
      recordId = insertResult.insertId
      console.log('已保存请求记录到数据库, ID:', recordId)
    } catch (dbError) {
      console.error('保存请求记录失败:', dbError)
    }
    
    // ===== 步骤2: 调用火山引擎API =====
    const response = await axios.post(
      `${volcBaseUrl}/images/generations`,
      requestBody,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${volcApiKey}`
        },
        timeout: 60000  // 60秒超时
      }
    )

    const apiDuration = Date.now() - startTime
    console.log('火山引擎API响应:', JSON.stringify(response.data, null, 2))

    // 检查响应
    if (response.data && response.data.data && response.data.data.length > 0) {
      const imageData = response.data.data[0]
      const tempImageUrl = imageData.url  // 临时URL(24小时过期)
      
      // ===== 步骤3: 下载图片并保存到OSS(永久存储) =====
      let permanentImageUrl = tempImageUrl
      
      try {
        console.log('开始下载图片并上传到OSS...')
        
        // 下载图片
        const imageResponse = await axios.get(tempImageUrl, {
          responseType: 'arraybuffer',
          timeout: 30000
        })
        
        const imageBuffer = Buffer.from(imageResponse.data)
        
        // 上传到OSS
        const OSS = require('ali-oss')
        const ossClient = new OSS({
          region: 'oss-cn-szfinance',
          accessKeyId: 'LTAI5t9cVPASvgKFkt1DsiZT',
          accessKeySecret: '******************************',
          bucket: 'hq-prd-e-zine',
          endpoint: 'oss-cn-szfinance.aliyuncs.com'
        })
        
        const timestamp = Date.now()
        const randomStr = Math.random().toString(36).substring(2, 8)
        const ossPath = `/agent/uat/ai-generated-images/${timestamp}-${randomStr}-${width}x${height}.jpg`
        
        const ossResult = await ossClient.put(ossPath, imageBuffer, {
          headers: {
            'Content-Type': 'image/jpeg',
            'Cache-Control': 'public, max-age=31536000'
          }
        })
        
        permanentImageUrl = ossResult.url
        console.log('图片已保存到OSS:', permanentImageUrl)
        
      } catch (ossError) {
        console.error('保存图片到OSS失败:', ossError.message)
        console.warn('将使用火山引擎临时URL(24小时有效期)')
      }
      
      // ===== 步骤4: 更新数据库记录 - 成功 =====
      if (recordId) {
        try {
          const updateSql = `
            UPDATE private_ai_images 
            SET response_status = 'success',
                response_image_url = ?,
                response_model = ?,
                response_created = ?,
                response_body = ?,
                image_url = ?,
                api_duration_ms = ?
            WHERE id = ?
          `
          const updateParams = [
            tempImageUrl,
            response.data.model || volcModel,
            response.data.created || null,
            JSON.stringify(response.data),
            permanentImageUrl,
            apiDuration,
            recordId
          ]
          
          await executeQuery(updateSql, updateParams)
          console.log('已更新数据库记录为成功状态')
        } catch (dbError) {
          console.error('更新成功记录失败:', dbError)
        }
      }
      
      res.json({
        code: '000000',
        message: '图片生成成功',
        data: {
          imageUrl: permanentImageUrl,
          tempUrl: tempImageUrl,
          prompt: prompt,
          size: imageSize,
          width: width,
          height: height,
          timestamp: new Date().toISOString(),
          recordId: recordId,
          isPermanent: permanentImageUrl !== tempImageUrl
        }
      })
    } else {
      throw new Error('API返回数据格式异常')
    }

  } catch (error) {
    const apiDuration = Date.now() - startTime
    console.error('生成图片失败:', error.response?.data || error.message)
    
    // 更新数据库记录 - 失败
    if (recordId) {
      try {
        const updateSql = `
          UPDATE private_ai_images 
          SET response_status = 'failed',
              response_error = ?,
              response_body = ?,
              api_duration_ms = ?
          WHERE id = ?
        `
        const errorInfo = error.response?.data || { message: error.message }
        const updateParams = [
          JSON.stringify(errorInfo),
          JSON.stringify(errorInfo),
          apiDuration,
          recordId
        ]
        
        await executeQuery(updateSql, updateParams)
      } catch (dbError) {
        console.error('更新失败记录失败:', dbError)
      }
    }

    res.status(500).json({
      code: '500000',
      message: '图片生成失败: ' + (error.response?.data?.error?.message || error.message),
      error: error.response?.data || error.message
    })
  }
})
```

### 3. 历史记录查询API

**端点:** `GET /api/poster/generation-history`

**功能:** 分页查询生成历史记录

```javascript
app.get('/api/poster/generation-history', async (req, res) => {
  try {
    const { page = 1, pageSize = 20, status, startDate, endDate } = req.query
    
    let whereClauses = ['is_deleted = 0']
    let params = []
    
    if (status) {
      whereClauses.push('response_status = ?')
      params.push(status)
    }
    
    if (startDate) {
      whereClauses.push('created_at >= ?')
      params.push(startDate)
    }
    
    if (endDate) {
      whereClauses.push('created_at <= ?')
      params.push(endDate)
    }
    
    const whereClause = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : ''
    
    // 查询总数
    const countSql = `SELECT COUNT(*) as total FROM private_ai_images ${whereClause}`
    const [countResult] = await executeQuery(countSql, params)
    const total = countResult.total
    
    // 查询列表
    const offset = (parseInt(page) - 1) * parseInt(pageSize)
    const limit = parseInt(pageSize)
    
    const listSql = `
      SELECT 
        id,
        request_prompt,
        request_width,
        request_height,
        request_size,
        request_seed,
        response_status,
        response_image_url as image_url,
        image_url as permanent_url,
        image_width,
        image_height,
        api_duration_ms,
        created_at
      FROM private_ai_images
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ${limit} OFFSET ${offset}
    `
    
    const list = await executeQuery(listSql, params)
    
    res.json({
      code: '000000',
      message: '查询成功',
      data: {
        list,
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        totalPages: Math.ceil(total / parseInt(pageSize))
      }
    })
  } catch (error) {
    console.error('查询生成历史失败:', error)
    res.status(500).json({
      code: '500000',
      message: '查询失败',
      error: error.message
    })
  }
})
```

### 4. 删除记录API

**端点:** `DELETE /api/poster/generation-history/:id`

**功能:** 软删除指定记录

```javascript
app.delete('/api/poster/generation-history/:id', async (req, res) => {
  try {
    const { id } = req.params
    
    const sql = `
      UPDATE private_ai_images 
      SET is_deleted = 1, deleted_at = CURRENT_TIMESTAMP
      WHERE id = ? AND is_deleted = 0
    `
    
    const result = await executeQuery(sql, [id])
    
    if (result.affectedRows === 0) {
      return res.status(404).json({
        code: '404000',
        message: '记录不存在'
      })
    }
    
    res.json({
      code: '000000',
      message: '删除成功'
    })
  } catch (error) {
    console.error('删除记录失败:', error)
    res.status(500).json({
      code: '500000',
      message: '删除失败',
      error: error.message
    })
  }
})
```

---

## 前端实现

### 1. 页面结构

**文件路径:** `frontend/src/views/PosterGeneration.vue`

**布局:**
```
┌──────────────────────────────────────────────────────┐
│  头部导航 (图像生成 / 历史记录)                        │
├──────────────────────┬───────────────────────────────┤
│                      │  右侧控制面板                  │
│   左侧预览区         │  ┌─────────────────────────┐ │
│   (瀑布流展示)       │  │ 参数 / 文案 (Tab切换)   │ │
│                      │  ├─────────────────────────┤ │
│   ┌──────┐ ┌──────┐ │  │                         │ │
│   │ 图片1│ │ 图片2│ │  │ • 提示词输入框          │ │
│   └──────┘ └──────┘ │  │ • 参考图片上传(多图)    │ │
│                      │  │ • 图片比例选择          │ │
│   ┌──────┐ ┌──────┐ │  │ • 分辨率选择            │ │
│   │ 图片3│ │ 图片4│ │  │ • 随机种子              │ │
│   └──────┘ └──────┘ │  │                         │ │
│                      │  └─────────────────────────┘ │
│                      │  [生成图片按钮]              │
└──────────────────────┴───────────────────────────────┘
```

### 2. 核心数据结构

```javascript
import { ref, reactive, computed } from 'vue'

// 当前标签页
const activeTab = ref('generate')  // 'generate' | 'history'

// 右侧面板模式
const panelMode = ref('params')  // 'params' | 'copywriting'

// 生成状态
const isGenerating = ref(false)

// 生成的图片列表
const generatedImages = ref([])

// 生成参数
const generationParams = reactive({
  prompt: '',                    // 提示词
  width: 2048,                   // 宽度
  height: 2048,                  // 高度
  seed: null,                    // 随机种子
  referenceImages: [],           // 参考图片预览URL列表(本地)
  referenceImageFiles: []        // 参考图片OSS URL列表(上传后)
})

// 预设尺寸
const presetSizes = [
  { name: '1:1', width: 2048, height: 2048 },
  { name: '4:3', width: 2304, height: 1728 },
  { name: '3:4', width: 1728, height: 2304 },
  { name: '16:9', width: 2560, height: 1440 },
  { name: '9:16', width: 1440, height: 2560 }
]
```

### 3. 参考图片上传 (多图支持)

```javascript
// 处理图片上传 (支持多图)
const handleImageUpload = async (event) => {
  const files = Array.from(event.target.files)
  if (files.length === 0) return

  // 检查数量限制 (最多4张)
  const remainingSlots = 4 - generationParams.referenceImages.length
  if (files.length > remainingSlots) {
    alert(`最多只能上传4张图片,当前还可以上传${remainingSlots}张`)
    event.target.value = ''
    return
  }

  for (const file of files) {
    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      alert(`文件 ${file.name} 格式不支持`)
      continue
    }

    // 验证文件大小(5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert(`文件 ${file.name} 大小超过5MB`)
      continue
    }

    try {
      // 显示本地预览
      const reader = new FileReader()
      reader.onload = (e) => {
        generationParams.referenceImages.push(e.target.result)
      }
      reader.readAsDataURL(file)

      // 上传到服务器
      const formData = new FormData()
      formData.append('image', file)

      const response = await fetch('/api/poster/upload-reference-image', {
        method: 'POST',
        body: formData
      })

      const result = await response.json()

      if (result.code === '000000') {
        generationParams.referenceImageFiles.push(result.data.url)
        console.log(`参考图片${generationParams.referenceImageFiles.length}上传成功:`, result.data.url)
      } else {
        alert(`图片上传失败: ${result.message}`)
        generationParams.referenceImages.pop()
      }
    } catch (error) {
      console.error('图片上传异常:', error)
      alert('图片上传失败,请重试')
      generationParams.referenceImages.pop()
    }
  }

  event.target.value = ''
}

// 删除指定索引的参考图片
const removeReferenceImage = (index) => {
  generationParams.referenceImages.splice(index, 1)
  generationParams.referenceImageFiles.splice(index, 1)
}
```

### 4. 调用生成API

```javascript
const generateImages = async () => {
  if (!canGenerate.value) return

  isGenerating.value = true
  
  try {
    console.log('开始调用AI图片生成接口...')
    
    // 构建请求体
    const requestBody = {
      prompt: generationParams.prompt,
      width: generationParams.width,
      height: generationParams.height,
      seed: generationParams.seed,
      watermark: false
    }

    // 如果有参考图片,添加到请求中
    if (generationParams.referenceImageFiles.length > 0) {
      // ⚠️ 关键: 多图模式传递图片数组
      requestBody.images = generationParams.referenceImageFiles
      console.log(`使用图生图模式,参考图片数量: ${requestBody.images.length}`)
    }
    
    // 调用后端API生成图片
    const response = await fetch('/api/poster/generate-image', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestBody)
    })
    
    const result = await response.json()
    console.log('API响应:', result)
    
    if (result.code === '000000' && result.data) {
      // 保存图片信息
      const imageInfo = {
        id: result.data.recordId || Date.now(),
        url: result.data.imageUrl,
        prompt: generationParams.prompt,
        width: generationParams.width,
        height: generationParams.height,
        seed: generationParams.seed,
        createdAt: new Date(),
        status: 'success'
      }
      
      generatedImages.value.unshift(imageInfo)
      console.log('图片生成成功')
    } else {
      throw new Error(result.message || '图片生成失败')
    }
  } catch (error) {
    console.error('图片生成失败:', error)
    alert(`图片生成失败：${error.message}`)
  } finally {
    isGenerating.value = false
  }
}
```

### 5. 文案字段发送到提示词

```javascript
// 发送字段到提示词框
const sendFieldToPrompt = (fieldName, fieldValue) => {
  // 格式化字段信息: "字段名: 字段值"
  const formattedText = `${fieldName}: ${fieldValue}`
  
  // 追加到提示词中(如果提示词已有内容,添加换行)
  if (generationParams.prompt.trim()) {
    generationParams.prompt += '\n' + formattedText
  } else {
    generationParams.prompt = formattedText
  }
  
  console.log('已将字段发送到提示词:', fieldName)
  
  // 显示成功提示Toast
  showSuccessToast(`已添加: ${fieldName}`)
}

// 显示成功提示Toast
const showSuccessToast = (message) => {
  const toast = document.createElement('div')
  toast.textContent = message
  toast.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #10b981;
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 9999;
    font-size: 14px;
    font-weight: 500;
    animation: slideInRight 0.3s ease-out;
  `
  
  document.body.appendChild(toast)
  
  // 2秒后移除
  setTimeout(() => {
    toast.style.animation = 'slideOutRight 0.3s ease-out'
    setTimeout(() => {
      document.body.removeChild(toast)
    }, 300)
  }, 2000)
}
```

### 6. 历史记录加载

```javascript
// 加载历史记录
const loadHistory = async (page = 1) => {
  isLoadingHistory.value = true
  try {
    const response = await fetch(`/api/poster/generation-history?page=${page}&pageSize=20`)
    const result = await response.json()
    
    if (result.code === '000000' && result.data) {
      // 将数据库记录转换为前端格式
      const historyImages = result.data.list.map(record => ({
        id: record.id,
        url: record.image_url || record.permanent_url,
        prompt: record.request_prompt,
        width: record.image_width || record.request_width,
        height: record.image_height || record.request_height,
        seed: record.request_seed,
        createdAt: new Date(record.created_at),
        status: record.response_status
      })).filter(img => img.status === 'success' && img.url)
      
      generatedImages.value = historyImages
      
      // 更新分页信息
      currentPage.value = result.data.page
      totalRecords.value = result.data.total
      totalPages.value = result.data.totalPages
    }
  } catch (error) {
    console.error('加载历史记录失败:', error)
  } finally {
    isLoadingHistory.value = false
  }
}
```

---

## 火山AI对接

### 1. API文档

**官方文档:** https://www.volcengine.com/docs/82379/1263512

**端点:** `POST https://ark.cn-beijing.volces.com/api/v3/images/generations`

### 2. 请求格式

#### 文生图请求示例

```bash
curl -X POST https://ark.cn-beijing.volces.com/api/v3/images/generations \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ARK_API_KEY" \
  -d '{
    "model": "doubao-seedream-4-0-250828",
    "prompt": "一只可爱的橙色小猫坐在花园里",
    "size": "2048x2048",
    "response_format": "url",
    "watermark": false,
    "seed": 12345,
    "sequential_image_generation": "disabled"
  }'
```

#### 图生图请求示例 (单图)

```bash
curl -X POST https://ark.cn-beijing.volces.com/api/v3/images/generations \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ARK_API_KEY" \
  -d '{
    "model": "doubao-seedream-4-0-250828",
    "prompt": "将这只猫的颜色改为白色",
    "image": "https://example.com/reference.jpg",
    "size": "2K",
    "response_format": "url",
    "sequential_image_generation": "disabled"
  }'
```

#### 图生图请求示例 (多图) ⚠️ 关键

```bash
curl -X POST https://ark.cn-beijing.volces.com/api/v3/images/generations \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ARK_API_KEY" \
  -d '{
    "model": "doubao-seedream-4-0-250828",
    "prompt": "将图1的服装换为图2的服装",
    "image": [
      "https://example.com/image1.jpg",
      "https://example.com/image2.jpg"
    ],
    "size": "2K",
    "response_format": "url",
    "sequential_image_generation": "disabled"
  }'
```

### 3. 响应格式

```json
{
  "data": [
    {
      "url": "https://tos-cn-beijing.volces.com/xxx/generated.jpg"
    }
  ],
  "model": "doubao-seedream-4-0-250828",
  "created": 1732345678
}
```

### 4. 关键参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `model` | string | 是 | 模型名称，固定值: `doubao-seedream-4-0-250828` |
| `prompt` | string | 是 | 文本提示词，描述想要生成的图片内容 |
| `image` | string/array | 否 | 参考图片URL，单图为字符串，多图为数组 |
| `size` | string | 是 | 图片尺寸，文生图用`宽x高`，图生图用`2K`/`4K` |
| `seed` | integer | 否 | 随机种子，范围0-2147483647，相同种子可重现图片 |
| `watermark` | boolean | 否 | 是否添加水印，默认false |
| `response_format` | string | 否 | 返回格式，固定值: `url` |
| `sequential_image_generation` | string | 否 | 是否组图，固定值: `disabled` |
| `guidance_scale` | float | 否 | 引导系数，范围1.0-20.0，默认7.5 |

### 5. 尺寸规范

**文生图模式:**
- 格式: `宽x高`，如 `2048x2048`
- 最小像素: 921600 (如 960x960)

**图生图模式:**
- 格式: `2K` 或 `4K`
- `2K`: 适用于像素数 < 4194304
- `4K`: 适用于像素数 ≥ 4194304

**常用预设尺寸:**
```javascript
const presetSizes = [
  { name: '1:1', width: 2048, height: 2048 },      // 正方形
  { name: '4:3', width: 2304, height: 1728 },     // 横向
  { name: '3:4', width: 1728, height: 2304 },     // 纵向
  { name: '16:9', width: 2560, height: 1440 },    // 宽屏
  { name: '9:16', width: 1440, height: 2560 }     // 竖屏
]
```

### 6. 错误处理

**常见错误码:**

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 401 | API密钥无效 | 检查Authorization头中的Bearer Token |
| 400 | 参数错误 | 检查prompt、size等必填参数 |
| 429 | 请求过于频繁 | 添加请求限流/重试机制 |
| 500 | 服务器错误 | 稍后重试或联系技术支持 |

**错误处理示例:**

```javascript
try {
  const response = await axios.post(volcApiUrl, requestBody, {
    headers: {
      'Authorization': `Bearer ${volcApiKey}`
    },
    timeout: 60000
  })
  
  // 处理成功响应
} catch (error) {
  if (error.response) {
    // HTTP错误响应
    console.error('火山API错误:', error.response.data)
    
    if (error.response.status === 401) {
      throw new Error('API密钥无效，请检查配置')
    } else if (error.response.status === 429) {
      throw new Error('请求过于频繁，请稍后重试')
    } else {
      throw new Error(error.response.data?.error?.message || '生成失败')
    }
  } else if (error.request) {
    // 网络错误
    throw new Error('网络连接失败，请检查网络')
  } else {
    // 其他错误
    throw new Error(error.message)
  }
}
```

---

## 完整代码示例

### 后端完整示例 (server.js)

```javascript
// ==================== 海报生成 - AI图片生成API ====================

const express = require('express')
const multer = require('multer')
const axios = require('axios')
const OSS = require('ali-oss')

const app = express()

// 配置multer
const upload = multer({
  storage: multer.memoryStorage(),
  limits: { fileSize: 5 * 1024 * 1024 }
})

/**
 * 参考图片上传接口
 */
app.post('/api/poster/upload-reference-image', upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: '400000',
        message: '未上传图片文件'
      })
    }

    const allowedMimes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedMimes.includes(req.file.mimetype)) {
      return res.status(400).json({
        code: '400001',
        message: '仅支持 JPG、PNG、WEBP 格式图片'
      })
    }

    const ossClient = new OSS({
      region: 'oss-cn-szfinance',
      accessKeyId: 'LTAI5t9cVPASvgKFkt1DsiZT',
      accessKeySecret: '******************************',
      bucket: 'hq-prd-e-zine',
      endpoint: 'oss-cn-szfinance.aliyuncs.com'
    })

    const timestamp = Date.now()
    const randomStr = Math.random().toString(36).substring(2, 8)
    const ext = req.file.originalname.split('.').pop() || 'jpg'
    const ossPath = `/agent/uat/ai-reference-images/${timestamp}-${randomStr}.${ext}`

    const result = await ossClient.put(ossPath, req.file.buffer, {
      headers: { 'Content-Type': req.file.mimetype }
    })

    res.json({
      code: '000000',
      message: '图片上传成功',
      data: {
        url: result.url,
        size: req.file.size,
        mimetype: req.file.mimetype
      }
    })
  } catch (error) {
    console.error('图片上传失败:', error)
    res.status(500).json({
      code: '500000',
      message: '图片上传失败: ' + error.message
    })
  }
})

/**
 * 火山引擎图片生成接口
 */
app.post('/api/poster/generate-image', async (req, res) => {
  const startTime = Date.now()
  let recordId = null
  
  try {
    const { prompt, width = 1024, height = 1024, seed, image, images, watermark = false } = req.body

    if (!prompt || typeof prompt !== 'string' || prompt.trim() === '') {
      return res.status(400).json({
        code: '400000',
        message: '提示词不能为空'
      })
    }

    const referenceImages = images || (image ? [image] : [])
    const isImageToImage = referenceImages.length > 0

    const volcApiKey = '480b4cd8-2d62-423d-8a59-bb6e47cf3776'
    const volcBaseUrl = 'https://ark.cn-beijing.volces.com/api/v3'
    const volcModel = 'doubao-seedream-4-0-250828'

    let imageSize
    if (isImageToImage) {
      const pixels = width * height
      imageSize = pixels >= 4194304 ? '4K' : '2K'
    } else {
      imageSize = `${width}x${height}`
    }

    const requestBody = {
      model: volcModel,
      prompt: prompt,
      size: imageSize,
      response_format: 'url',
      watermark: watermark,
      sequential_image_generation: 'disabled'
    }
    
    if (isImageToImage) {
      requestBody.image = referenceImages.length === 1 ? referenceImages[0] : referenceImages
    }
    
    if (seed !== undefined && seed !== null && seed !== '' && seed !== -1) {
      const seedValue = parseInt(seed)
      if (!isNaN(seedValue) && seedValue >= 0 && seedValue <= 2147483647) {
        requestBody.seed = seedValue
      }
    }

    // 保存请求记录
    const insertSql = `
      INSERT INTO private_ai_images 
      (request_prompt, request_width, request_height, request_size, 
       request_seed, request_watermark, request_body, response_status)
      VALUES (?, ?, ?, ?, ?, ?, ?, 'pending')
    `
    const insertResult = await executeQuery(insertSql, [
      prompt, width, height, imageSize,
      requestBody.seed || null,
      watermark ? 1 : 0,
      JSON.stringify(requestBody)
    ])
    recordId = insertResult.insertId

    // 调用火山API
    const response = await axios.post(
      `${volcBaseUrl}/images/generations`,
      requestBody,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${volcApiKey}`
        },
        timeout: 60000
      }
    )

    const apiDuration = Date.now() - startTime

    if (response.data && response.data.data && response.data.data.length > 0) {
      const tempImageUrl = response.data.data[0].url
      let permanentImageUrl = tempImageUrl
      
      // 下载并上传到OSS
      try {
        const imageResponse = await axios.get(tempImageUrl, {
          responseType: 'arraybuffer',
          timeout: 30000
        })
        
        const imageBuffer = Buffer.from(imageResponse.data)
        
        const ossClient = new OSS({
          region: 'oss-cn-szfinance',
          accessKeyId: 'LTAI5t9cVPASvgKFkt1DsiZT',
          accessKeySecret: '******************************',
          bucket: 'hq-prd-e-zine',
          endpoint: 'oss-cn-szfinance.aliyuncs.com'
        })
        
        const timestamp = Date.now()
        const randomStr = Math.random().toString(36).substring(2, 8)
        const ossPath = `/agent/uat/ai-generated-images/${timestamp}-${randomStr}-${width}x${height}.jpg`
        
        const ossResult = await ossClient.put(ossPath, imageBuffer, {
          headers: { 'Content-Type': 'image/jpeg' }
        })
        
        permanentImageUrl = ossResult.url
      } catch (ossError) {
        console.error('保存到OSS失败:', ossError.message)
      }
      
      // 更新数据库
      await executeQuery(`
        UPDATE private_ai_images 
        SET response_status = 'success',
            response_image_url = ?,
            image_url = ?,
            api_duration_ms = ?
        WHERE id = ?
      `, [tempImageUrl, permanentImageUrl, apiDuration, recordId])
      
      res.json({
        code: '000000',
        message: '图片生成成功',
        data: {
          imageUrl: permanentImageUrl,
          width: width,
          height: height,
          recordId: recordId
        }
      })
    } else {
      throw new Error('API返回数据格式异常')
    }

  } catch (error) {
    console.error('生成图片失败:', error)
    
    if (recordId) {
      await executeQuery(`
        UPDATE private_ai_images 
        SET response_status = 'failed',
            response_error = ?
        WHERE id = ?
      `, [JSON.stringify(error.response?.data || { message: error.message }), recordId])
    }

    res.status(500).json({
      code: '500000',
      message: '图片生成失败: ' + (error.response?.data?.error?.message || error.message)
    })
  }
})

/**
 * 历史记录查询接口
 */
app.get('/api/poster/generation-history', async (req, res) => {
  try {
    const { page = 1, pageSize = 20 } = req.query
    
    const countSql = `SELECT COUNT(*) as total FROM private_ai_images WHERE is_deleted = 0`
    const [{ total }] = await executeQuery(countSql)
    
    const offset = (parseInt(page) - 1) * parseInt(pageSize)
    const limit = parseInt(pageSize)
    
    const listSql = `
      SELECT 
        id, request_prompt, request_width, request_height,
        response_image_url as image_url, image_url as permanent_url,
        created_at
      FROM private_ai_images
      WHERE is_deleted = 0
      ORDER BY created_at DESC
      LIMIT ${limit} OFFSET ${offset}
    `
    
    const list = await executeQuery(listSql)
    
    res.json({
      code: '000000',
      message: '查询成功',
      data: {
        list,
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        totalPages: Math.ceil(total / parseInt(pageSize))
      }
    })
  } catch (error) {
    res.status(500).json({
      code: '500000',
      message: '查询失败',
      error: error.message
    })
  }
})

/**
 * 删除记录接口
 */
app.delete('/api/poster/generation-history/:id', async (req, res) => {
  try {
    const { id } = req.params
    
    const result = await executeQuery(`
      UPDATE private_ai_images 
      SET is_deleted = 1, deleted_at = CURRENT_TIMESTAMP
      WHERE id = ? AND is_deleted = 0
    `, [id])
    
    if (result.affectedRows === 0) {
      return res.status(404).json({
        code: '404000',
        message: '记录不存在'
      })
    }
    
    res.json({
      code: '000000',
      message: '删除成功'
    })
  } catch (error) {
    res.status(500).json({
      code: '500000',
      message: '删除失败',
      error: error.message
    })
  }
})
```

### 前端完整示例 (PosterGeneration.vue)

```vue
<template>
  <div class="h-full flex flex-col bg-gray-50">
    <!-- 头部导航 -->
    <header class="bg-white border-b border-gray-200 px-6 py-4">
      <div class="flex items-center justify-between">
        <h1 class="text-xl font-bold text-gray-900">AI图片生成</h1>
        
        <div class="flex bg-gray-100 rounded-lg p-1">
          <button 
            :class="[
              'px-4 py-2 rounded-md text-sm font-medium transition-all',
              activeTab === 'generate' 
                ? 'bg-white text-gray-900 shadow-sm' 
                : 'text-gray-600 hover:text-gray-900'
            ]"
            @click="activeTab = 'generate'"
          >
            图像生成
          </button>
          <button 
            :class="[
              'px-4 py-2 rounded-md text-sm font-medium transition-all',
              activeTab === 'history' 
                ? 'bg-white text-gray-900 shadow-sm' 
                : 'text-gray-600 hover:text-gray-900'
            ]"
            @click="activeTab = 'history'; loadHistory(1)"
          >
            历史记录
          </button>
        </div>
      </div>
    </header>

    <!-- 主内容区域 -->
    <main class="flex-1 flex overflow-hidden">
      <!-- 左侧预览区 -->
      <section class="flex-1 bg-white m-4 rounded-lg shadow-sm border overflow-auto">
        <div v-if="generatedImages.length === 0 && !isGenerating" class="h-full flex flex-col items-center justify-center text-gray-400">
          <p class="text-lg font-medium">还没有生成图片</p>
        </div>
        
        <div v-else-if="isGenerating" class="h-full flex items-center justify-center">
          <div class="text-center">
            <svg class="animate-spin w-16 h-16 text-purple-500 mx-auto" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path>
            </svg>
            <p class="mt-4 text-lg font-medium text-gray-700">正在生成图片...</p>
          </div>
        </div>
        
        <div v-else class="p-6 columns-3 gap-4">
          <div 
            v-for="image in generatedImages" 
            :key="image.id"
            class="break-inside-avoid mb-4 group relative rounded-lg overflow-hidden border hover:shadow-lg transition-all cursor-pointer"
            @click="openPreview(image)"
          >
            <img :src="image.url" :alt="image.prompt" class="w-full h-auto" />
            
            <div class="absolute bottom-2 right-2 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <button @click.stop="downloadImage(image)" class="w-8 h-8 flex items-center justify-center">
                <svg class="w-5 h-5 text-white drop-shadow-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- 右侧控制面板 -->
      <aside class="w-96 bg-white m-4 ml-0 rounded-lg shadow-sm border flex flex-col">
        <div class="p-6 space-y-6 flex-1 overflow-y-auto">
          <!-- 提示词输入 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              正向提示词 <span class="text-red-500">*</span>
            </label>
            <textarea 
              v-model="generationParams.prompt"
              placeholder="描述您想要生成的图片内容..."
              rows="4"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 resize-none text-sm"
            ></textarea>
          </div>

          <!-- 参考图片上传 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              参考图片(可选)
              <span class="text-xs text-gray-500 ml-2">支持多图上传 (最多4张)</span>
            </label>
            
            <div class="grid grid-cols-2 gap-3">
              <div 
                v-for="(image, index) in generationParams.referenceImages" 
                :key="index"
                class="relative group aspect-square"
              >
                <img :src="image" class="w-full h-full object-cover rounded-lg border" />
                <div class="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                  <button
                    @click="removeReferenceImage(index)"
                    class="px-3 py-1.5 bg-red-500 text-white rounded-lg hover:bg-red-600 text-xs font-medium"
                  >
                    删除
                  </button>
                </div>
              </div>

              <div v-if="generationParams.referenceImages.length < 4" class="relative aspect-square">
                <input 
                  type="file" 
                  ref="imageUploadInput"
                  accept="image/*"
                  multiple
                  @change="handleImageUpload"
                  class="hidden"
                />
                <button
                  @click="$refs.imageUploadInput.click()"
                  class="w-full h-full border-2 border-dashed border-gray-300 rounded-lg hover:border-purple-400 hover:bg-purple-50 flex flex-col items-center justify-center gap-2"
                >
                  <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                  </svg>
                  <span class="text-xs text-gray-600">添加图片</span>
                </button>
              </div>
            </div>
          </div>

          <!-- 图片比例 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">图片比例</label>
            <div class="grid grid-cols-4 gap-2">
              <button 
                v-for="size in presetSizes"
                :key="size.name"
                @click="selectPresetSize(size)"
                :class="[
                  'px-3 py-2 text-xs font-medium rounded-lg border transition-all',
                  currentAspectRatio === size.name
                    ? 'bg-purple-100 border-purple-300 text-purple-700'
                    : 'bg-white border-gray-200 text-gray-600 hover:border-gray-300'
                ]"
              >
                {{ size.name }}
              </button>
            </div>
          </div>

          <!-- 随机种子 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">随机种子</label>
            <div class="flex items-center gap-2">
              <input 
                v-model.number="generationParams.seed" 
                type="number" 
                placeholder="留空随机"
                class="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-purple-500"
              />
              <button 
                @click="generateRandomSeed"
                class="px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 text-sm font-medium"
              >
                随机
              </button>
            </div>
          </div>
        </div>

        <!-- 底部操作栏 -->
        <div class="p-6 border-t border-gray-200 space-y-3">
          <button 
            @click="generateImages" 
            :disabled="!canGenerate || isGenerating"
            class="w-full px-6 py-3 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg font-medium hover:from-purple-600 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed transition-all shadow-md hover:shadow-lg flex items-center justify-center gap-2"
          >
            <svg v-if="isGenerating" class="animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path>
            </svg>
            {{ isGenerating ? '生成中...' : '生成图片' }}
          </button>
        </div>
      </aside>
    </main>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'

const activeTab = ref('generate')
const isGenerating = ref(false)
const generatedImages = ref([])
const currentAspectRatio = ref('1:1')

const generationParams = reactive({
  prompt: '',
  width: 2048,
  height: 2048,
  seed: null,
  referenceImages: [],
  referenceImageFiles: []
})

const presetSizes = [
  { name: '1:1', width: 2048, height: 2048 },
  { name: '4:3', width: 2304, height: 1728 },
  { name: '3:4', width: 1728, height: 2304 },
  { name: '16:9', width: 2560, height: 1440 },
  { name: '9:16', width: 1440, height: 2560 }
]

const canGenerate = computed(() => {
  return generationParams.prompt.trim().length > 0
})

const selectPresetSize = (size) => {
  currentAspectRatio.value = size.name
  generationParams.width = size.width
  generationParams.height = size.height
}

const generateRandomSeed = () => {
  generationParams.seed = Math.floor(Math.random() * 4294967295)
}

const handleImageUpload = async (event) => {
  const files = Array.from(event.target.files)
  if (files.length === 0) return

  const remainingSlots = 4 - generationParams.referenceImages.length
  if (files.length > remainingSlots) {
    alert(`最多只能上传4张图片`)
    event.target.value = ''
    return
  }

  for (const file of files) {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      alert(`文件格式不支持`)
      continue
    }

    if (file.size > 5 * 1024 * 1024) {
      alert(`文件大小超过5MB`)
      continue
    }

    try {
      const reader = new FileReader()
      reader.onload = (e) => {
        generationParams.referenceImages.push(e.target.result)
      }
      reader.readAsDataURL(file)

      const formData = new FormData()
      formData.append('image', file)

      const response = await fetch('/api/poster/upload-reference-image', {
        method: 'POST',
        body: formData
      })

      const result = await response.json()

      if (result.code === '000000') {
        generationParams.referenceImageFiles.push(result.data.url)
      }
    } catch (error) {
      console.error('图片上传异常:', error)
    }
  }

  event.target.value = ''
}

const removeReferenceImage = (index) => {
  generationParams.referenceImages.splice(index, 1)
  generationParams.referenceImageFiles.splice(index, 1)
}

const generateImages = async () => {
  if (!canGenerate.value) return

  isGenerating.value = true
  
  try {
    const requestBody = {
      prompt: generationParams.prompt,
      width: generationParams.width,
      height: generationParams.height,
      seed: generationParams.seed,
      watermark: false
    }

    if (generationParams.referenceImageFiles.length > 0) {
      requestBody.images = generationParams.referenceImageFiles
    }
    
    const response = await fetch('/api/poster/generate-image', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestBody)
    })
    
    const result = await response.json()
    
    if (result.code === '000000' && result.data) {
      const imageInfo = {
        id: result.data.recordId || Date.now(),
        url: result.data.imageUrl,
        prompt: generationParams.prompt,
        width: generationParams.width,
        height: generationParams.height,
        seed: generationParams.seed,
        createdAt: new Date()
      }
      
      generatedImages.value.unshift(imageInfo)
    }
  } catch (error) {
    alert(`图片生成失败：${error.message}`)
  } finally {
    isGenerating.value = false
  }
}

const loadHistory = async (page = 1) => {
  try {
    const response = await fetch(`/api/poster/generation-history?page=${page}&pageSize=20`)
    const result = await response.json()
    
    if (result.code === '000000') {
      generatedImages.value = result.data.list.map(record => ({
        id: record.id,
        url: record.image_url || record.permanent_url,
        prompt: record.request_prompt,
        width: record.request_width,
        height: record.request_height
      }))
    }
  } catch (error) {
    console.error('加载历史记录失败:', error)
  }
}
</script>
```

---

## 常见问题

### Q1: 火山API返回401错误

**原因:** API密钥无效或过期

**解决方案:**
1. 检查`.env`文件中的`VOLC_API_KEY`是否正确
2. 确认Authorization请求头格式: `Bearer ${VOLC_API_KEY}`
3. 登录火山控制台重新生成API密钥

### Q2: 图片生成失败，提示"尺寸不符合要求"

**原因:** 图片像素数未达到最低要求(921600)

**解决方案:**
- 文生图模式: 确保 `width * height >= 921600`
- 图生图模式: 使用`2K`或`4K`而非具体尺寸

### Q3: 多图上传后只有第一张生效

**原因:** 未正确传递images数组

**解决方案:**
检查后端代码中的关键部分：
```javascript
// ❌ 错误写法
requestBody.image = referenceImages[0]

// ✅ 正确写法
requestBody.image = referenceImages.length === 1 
  ? referenceImages[0]  // 单图: 字符串
  : referenceImages     // 多图: 数组
```

### Q4: 图片URL 24小时后失效

**原因:** 火山返回的是临时URL

**解决方案:**
实现了自动下载并上传到OSS的逻辑：
1. 火山API返回临时URL
2. 后端下载图片Buffer
3. 上传到阿里云OSS
4. 保存永久URL到数据库

### Q5: OSS上传失败

**可能原因:**
1. AccessKeyId/Secret配置错误
2. Bucket不存在或无写入权限
3. 网络连接问题

**解决方案:**
1. 验证OSS配置参数
2. 检查Bucket权限设置(建议公共读)
3. 查看后端日志获取详细错误信息
4. 降级方案: 使用火山临时URL(24小时有效)

### Q6: 数据库连接失败

**排查步骤:**
1. 检查数据库地址、端口是否正确
2. 确认用户名密码是否正确
3. 检查数据库防火墙规则
4. 验证数据库表`private_ai_images`是否存在

### Q7: 前端跨域问题

**解决方案:**
在后端添加CORS中间件:
```javascript
const cors = require('cors')
app.use(cors())
```

或配置Vite代理:
```javascript
// vite.config.js
export default {
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true
      }
    }
  }
}
```

---

## 总结

本文档详细描述了火山AI图片生成功能的完整集成方案，包括：

1. ✅ **环境配置**: 火山API密钥、阿里云OSS、数据库连接
2. ✅ **数据库设计**: 完整的记录表结构和索引
3. ✅ **后端实现**: 图片上传、AI生成、历史查询、删除等API
4. ✅ **前端实现**: 界面交互、多图上传、文案联动等功能
5. ✅ **火山对接**: 文生图、单图/多图图生图的详细说明
6. ✅ **完整代码**: 可直接复制使用的前后端代码示例
7. ✅ **问题排查**: 常见错误和解决方案

### 核心要点提醒


⚠️ **关键实现细节:**
- 多图图生图: `requestBody.image = referenceImages` (数组)
- OSS永久存储: 防止火山临时URL过期
- 数据库记录: 完整保存请求和响应，便于追踪

---

**文档结束**

如有疑问，请参考:
- 火山引擎官方文档: https://www.volcengine.com/docs/82379/1263512
- 阿里云OSS文档: https://help.aliyun.com/product/31815.html
