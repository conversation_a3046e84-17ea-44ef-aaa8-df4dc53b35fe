# 配置文件重构说明

## 📋 重构概述

将配置文件从**功能维度**重构为**API提供商维度**,使配置更清晰、更易扩展。

---

## 🔄 配置结构对比

### 旧结构 (功能维度) ❌

```json
{
  "api_keys": {
    "image_generation": "key1",
    "video_generation": "key2"
  },
  "volcengine": {
    "base_url": "...",
    "model": "..."
  }
}
```

**问题**:
- API密钥与提供商信息分离
- 难以管理多个提供商
- 切换提供商不方便
- 配置结构不清晰

### 新结构 (提供商维度) ✅

```json
{
  "ai_providers": {
    "volcengine": {
      "api_key": "key1",
      "base_url": "...",
      "models": {
        "image_generation": {...}
      }
    },
    "openai": {
      "api_key": "key2",
      "base_url": "...",
      "models": {
        "image_generation": {...}
      }
    }
  },
  "active_providers": {
    "image_generation": "volcengine"
  }
}
```

**优势**:
- ✅ 提供商配置集中管理
- ✅ API密钥与提供商绑定
- ✅ 易于添加新提供商
- ✅ 灵活切换提供商
- ✅ 层级结构清晰

---

## 🏗️ 新配置结构详解

### 1. AI服务提供商 (`ai_providers`)

每个提供商一个配置块:

```json
{
  "ai_providers": {
    "提供商名称": {
      "api_key": "API密钥",
      "base_url": "API地址",
      "timeout": 超时时间,
      "models": {
        "功能名称": {
          "model_name": "模型显示名",
          "model_id": "实际模型ID",
          "其他配置": "..."
        }
      }
    }
  }
}
```

**示例 - 火山引擎**:
```json
{
  "volcengine": {
    "api_key": "480b4cd8-2d62-423d-8a59-bb6e47cf3776",
    "base_url": "https://ark.cn-beijing.volces.com/api/v3",
    "timeout": 60,
    "models": {
      "image_generation": {
        "model_name": "doubao-seedream-4-0-250828",
        "model_id": "doubao-seedream-4-0-250828",
        "max_resolution": "2560x2560",
        "preset_sizes": [...]
      }
    }
  }
}
```

### 2. 激活的提供商 (`active_providers`)

指定每个功能使用哪个提供商:

```json
{
  "active_providers": {
    "image_generation": "volcengine",  // 当前使用火山引擎
    "video_generation": null,          // 未启用
    "voice_recognition": null,
    "quality_check": null
  }
}
```

**切换提供商**: 只需修改这个值!
```json
"image_generation": "openai"  // 切换到OpenAI
```

---

## 🎯 使用场景示例

### 场景1: 添加OpenAI支持

在 `ai_providers` 下添加:

```json
{
  "ai_providers": {
    "volcengine": {...},
    "openai": {
      "api_key": "sk-xxxxxxxxxxxxxxx",
      "base_url": "https://api.openai.com/v1",
      "timeout": 60,
      "models": {
        "image_generation": {
          "model_name": "dall-e-3",
          "model_id": "dall-e-3",
          "max_resolution": "1024x1024"
        }
      }
    }
  }
}
```

### 场景2: 切换到OpenAI

修改 `active_providers`:

```json
{
  "active_providers": {
    "image_generation": "openai"  // 从volcengine改为openai
  }
}
```

### 场景3: 同时支持多个模型

在同一提供商下添加多个模型:

```json
{
  "volcengine": {
    "api_key": "...",
    "base_url": "...",
    "models": {
      "image_generation": {
        "model_id": "doubao-seedream-4-0-250828"
      },
      "video_generation": {
        "model_id": "doubao-video-model"
      },
      "voice_recognition": {
        "model_id": "doubao-voice-model"
      }
    }
  }
}
```

---

## 💻 代码适配

### 旧代码 (已废弃)

```python
# ❌ 旧方式
self.api_key = config['api_keys']['image_generation']
self.base_url = config['volcengine']['base_url']
self.model = config['volcengine']['model']
```

### 新代码 (当前实现)

```python
# ✅ 新方式
# 1. 获取激活的提供商
active_provider = config['active_providers']['image_generation']

# 2. 加载提供商配置
provider_config = config['ai_providers'][active_provider]
self.api_key = provider_config['api_key']
self.base_url = provider_config['base_url']

# 3. 加载模型配置
model_config = provider_config['models']['image_generation']
self.model = model_config['model_id']
```

**优势**:
- 自动适配不同提供商
- 无需修改代码即可切换
- 配置驱动,更灵活

---

## 📝 迁移步骤

### 对于现有项目

1. **备份旧配置**
```powershell
copy config.json config.json.bak
```

2. **应用新配置结构**
使用新版 `config.json`

3. **重启服务**
```powershell
python main.py
```

4. **验证功能**
访问 http://localhost:8000/api/health

---

## 🔍 配置验证

### 验证JSON格式

```powershell
python -m json.tool config/config.json
```

### 验证提供商配置

```python
import json

config = json.load(open('config/config.json', encoding='utf-8'))

# 检查必需字段
assert 'ai_providers' in config
assert 'active_providers' in config

# 检查激活的提供商是否存在
active = config['active_providers']['image_generation']
assert active in config['ai_providers']

print("✓ 配置验证通过!")
```

---

## 🚀 后续扩展示例

### 添加更多AI提供商

```json
{
  "ai_providers": {
    "volcengine": {...},
    "openai": {...},
    "anthropic": {
      "api_key": "sk-ant-xxxxx",
      "base_url": "https://api.anthropic.com",
      "models": {
        "image_generation": {
          "model_id": "claude-image-v1"
        }
      }
    },
    "midjourney": {
      "api_key": "mj-xxxxx",
      "base_url": "https://api.midjourney.com",
      "models": {
        "image_generation": {
          "model_id": "midjourney-v6"
        }
      }
    }
  }
}
```

### A/B测试不同模型

```json
{
  "active_providers": {
    "image_generation": "volcengine",  // 主要使用火山
    "image_generation_backup": "openai"  // 备用OpenAI
  }
}
```

---

## ⚠️ 注意事项

1. **向后兼容**: 旧代码不再兼容新配置结构
2. **必需字段**: `api_key`, `base_url`, `models` 是必需的
3. **提供商名称**: 使用小写英文,不含特殊字符
4. **配置同步**: 修改配置后需重启服务

---

## 📚 相关文档

- [配置文件说明](配置文件说明.md) - 详细的配置字段说明
- [快速开始](快速开始.md) - 项目快速入门
- [图片生成功能说明](图片生成功能说明.md) - 图片生成功能详解

---

**重构完成! 配置结构更清晰、更易扩展!** 🎉
