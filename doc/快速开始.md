# 快速开始 - 图片生成功能

## 🎯 功能说明

已成功集成火山引擎豆包AI图片生成功能,支持:
- ✅ 文生图(根据文字描述生成图片)
- ✅ 多种预设尺寸(1:1, 4:3, 3:4, 16:9, 9:16)
- ✅ 随机种子控制
- ✅ 高分辨率输出(最高2560x2560)

## 📦 环境准备

### 1. Python依赖安装

```bash
cd d:/private/private01/after
pip install -r requirements.txt
```

需要的包:
- fastapi==0.109.0
- uvicorn==0.27.0
- pydantic==2.5.3
- requests==2.31.0

### 2. 配置文件

配置文件 `after/config/config.json` 已包含火山AI密钥:
```json
{
  "api_keys": {
    "image_generation": "480b4cd8-2d62-423d-8a59-bb6e47cf3776"
  },
  "volcengine": {
    "base_url": "https://ark.cn-beijing.volces.com/api/v3",
    "model": "doubao-seedream-4-0-250828"
  }
}
```

## 🚀 启动步骤

### 方式1: 快速启动(推荐)

**步骤1: 启动后端**
```bash
cd d:/private/private01/after
python main.py
```

看到以下输出表示启动成功:
```
==================================================
AI工具导航后端服务
==================================================
服务地址: http://0.0.0.0:8000
API文档: http://0.0.0.0:8000/docs
==================================================
INFO:     Started server process
INFO:     Uvicorn running on http://0.0.0.0:8000
```

**步骤2: 打开前端页面**

直接在浏览器打开:
```
d:/private/private01/before/html/index.html
```

**步骤3: 进入图片生成**

点击"图片生成"卡片,进入生成页面。

### 方式2: 使用本地服务器

如果直接打开HTML文件遇到CORS问题,使用Python内置服务器:

```bash
# 启动前端服务器
cd d:/private/private01/before/html
python -m http.server 3000
```

然后访问: http://localhost:3000

## 🧪 测试验证

### 1. 运行自动化测试

```bash
cd d:/private/private01
python test/test_image_generation.py
```

应该看到类似输出:
```
==================================================
🚀 开始运行图片生成功能测试
==================================================

测试1: 提示词验证
✅ 测试通过!

测试2: 错误处理
✅ 测试通过!

测试3: 文生图 (Text-to-Image)
✅ 测试通过!
图片URL: https://tos-cn-beijing.volces.com/xxx.jpg
耗时: 8234ms

📊 测试结果汇总
总计: 4/4 通过

🎉 所有测试通过!
```

### 2. 手动测试API

使用curl测试:
```bash
curl -X POST http://localhost:8000/api/image-generation \
  -H "Content-Type: application/json" \
  -d "{\"prompt\":\"一只可爱的猫咪\",\"width\":2048,\"height\":2048}"
```

或访问API文档: http://localhost:8000/docs

## 📝 使用示例

### 示例1: 基础文生图

1. 输入提示词: `一只可爱的橙色小猫坐在花园里`
2. 选择比例: 1:1
3. 点击"生成图片"
4. 等待10-30秒
5. 图片显示在左侧,点击下载保存

### 示例2: 使用固定种子

1. 输入提示词: `未来科技城市,霓虹灯光`
2. 设置种子: `12345`
3. 生成图片
4. 使用相同种子和提示词可以生成相同图片

### 示例3: 不同尺寸

尝试不同比例:
- **1:1** (2048x2048) - 适合头像、Logo
- **4:3** (2304x1728) - 适合横向海报
- **3:4** (1728x2304) - 适合竖向海报
- **16:9** (2560x1440) - 适合宽屏壁纸
- **9:16** (1440x2560) - 适合手机壁纸

## ⚠️ 注意事项

### 图片URL有效期
火山AI返回的图片URL **仅24小时有效**,请及时下载保存!

### CORS配置
如遇跨域问题,确保:
1. 后端已启动
2. 前端访问地址在CORS白名单中
3. 或使用本地服务器启动前端

### 生成时间
- 通常需要10-30秒
- 高分辨率可能需要更长时间
- 请耐心等待,不要重复点击

## 🔧 故障排查

### 问题1: 后端启动失败
```
错误: ModuleNotFoundError: No module named 'fastapi'
解决: pip install -r requirements.txt
```

### 问题2: API调用失败
```
错误: API调用失败: invalid api key
解决: 检查config.json中的API密钥是否正确
```

### 问题3: CORS错误
```
错误: Access to fetch blocked by CORS policy
解决: 
1. 确保后端已启动
2. 使用 python -m http.server 启动前端
```

### 问题4: 图片无法显示
```
错误: 图片URL返回404
原因: 图片URL已过期(超过24小时)
解决: 重新生成图片
```

## 📚 API接口文档

### 生成图片

**接口:** `POST /api/image-generation`

**请求参数:**
```json
{
  "prompt": "图片描述",      // 必填
  "width": 2048,             // 可选,默认2048
  "height": 2048,            // 可选,默认2048
  "seed": 12345,             // 可选,留空随机
  "watermark": false         // 可选,默认false
}
```

**响应示例:**
```json
{
  "success": true,
  "message": "图片生成成功",
  "data": {
    "image_url": "https://xxx.jpg",
    "prompt": "图片描述",
    "width": 2048,
    "height": 2048,
    "seed": 12345,
    "duration_ms": 8234
  }
}
```

### 获取预设尺寸

**接口:** `GET /api/image-generation/preset-sizes`

**响应示例:**
```json
{
  "success": true,
  "data": [
    {"name": "1:1", "width": 2048, "height": 2048},
    {"name": "4:3", "width": 2304, "height": 1728}
  ]
}
```

## 📞 技术支持

- 项目文档: `doc/图片生成功能说明.md`
- API文档: http://localhost:8000/docs
- 火山引擎文档: https://www.volcengine.com/docs/82379/1263512

## 🎉 下一步

功能已完成并可以使用!

**建议扩展:**
1. 实现图生图功能(需要图片上传)
2. 添加历史记录保存(需要数据库)
3. OSS永久存储(避免24小时过期)
4. 批量生成功能
5. 图片编辑功能

祝使用愉快! 🚀
