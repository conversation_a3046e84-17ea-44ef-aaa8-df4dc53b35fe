# 语音识别功能使用指南

## 功能概述

基于讯飞AI实现的实时语音识别系统,支持:
- ✅ **实时转写**: 边说边显示文字
- ✅ **说话人盲分**: 自动区分不同说话人
- ✅ **移动端兼容**: 支持PC和手机浏览器录音
- ✅ **会议场景**: 适用于多人会议实时记录

## 技术架构

### 后端 (Python + FastAPI)
- **WebSocket服务**: 实时双向通信
- **讯飞AI对接**: 流式语音识别API
- **说话人识别**: vinfo参数启用盲分功能

### 前端 (原生JavaScript)
- **MediaDevices API**: 浏览器麦克风录音
- **AudioContext**: 音频处理和采样率转换
- **ScriptProcessor**: 实时PCM数据提取
- **WebSocket**: 与后端实时通信

## 配置信息

### 讯飞API配置
位置: `after/config/config.json`

```json
{
  "ai_providers": {
    "xfyun": {
      "app_id": "4ff6d5e8",
      "api_key": "a2d8619f4d0d918d61957bd647fe0c7b",
      "api_secret": "OWU1ZjMyMDM2ZmNiMjllYTg1OTM2ZTQ4",
      "base_url": "wss://ws-api.xfyun.cn/v2/iat",
      "models": {
        "voice_recognition": {
          "domain": "iat",
          "language": "zh_cn",
          "accent": "mandarin",
          "vad_eos": 10000,
          "enable_speaker_diarization": true
        }
      }
    }
  },
  "active_providers": {
    "voice_recognition": "xfyun"
  }
}
```

### 音频参数
- **采样率**: 16000Hz (讯飞要求)
- **声道**: 单声道
- **编码格式**: PCM 16位
- **数据传输**: Base64编码

## 使用步骤

### 1. 启动后端服务
```bash
cd after
python main.py
```

服务地址: `http://127.0.0.1:8000`
WebSocket端点: `ws://127.0.0.1:8000/ws/voice-recognition`

### 2. 启动前端服务
```bash
# 使用启动脚本
启动项目.bat

# 或手动启动
cd before
python -m http.server 3000
```

前端地址: `http://localhost:3000/html/`

### 3. 访问语音识别页面
点击主页的"声音识别"卡片,或直接访问:
`http://localhost:3000/html/voice-recognition.html`

### 4. 开始录音
1. 点击"开始录音"按钮
2. 授权浏览器麦克风权限
3. 开始说话,页面实时显示识别文字
4. 不同说话人以不同颜色区分
5. 点击"停止录音"结束

## 数据流程

```
用户说话
  ↓
浏览器麦克风录音
  ↓
AudioContext处理 (16kHz采样)
  ↓
ScriptProcessor提取PCM数据
  ↓
Base64编码
  ↓
WebSocket发送到后端
  ↓
后端转发到讯飞API
  ↓
讯飞返回识别结果
  ↓
后端解析说话人和文本
  ↓
WebSocket返回前端
  ↓
页面实时展示
```

## 前端WebSocket消息格式

### 发送给后端 (音频数据)
```json
{
  "type": "audio",
  "audio": "base64_encoded_pcm_data"
}
```

### 发送给后端 (停止信号)
```json
{
  "type": "stop"
}
```

### 后端返回 (识别结果)
```json
{
  "type": "result",
  "text": "识别的文字",
  "speaker": 0,
  "is_final": false
}
```

### 后端返回 (错误信息)
```json
{
  "type": "error",
  "code": 10013,
  "message": "错误描述"
}
```

## 说话人盲分说明

### 颜色映射
- **说话人1**: 紫色 (#667eea)
- **说话人2**: 粉色 (#f093fb)
- **说话人3**: 绿色 (#4ade80)
- **说话人4**: 黄色 (#fbbf24)
- **未知**: 灰色 (#94a3b8)

### 工作原理
- 讯飞API通过声纹特征自动区分说话人
- 无需预先训练,实时盲分
- `vinfo: 1` 参数启用说话人识别
- `rl` 字段返回说话人ID

## 移动端适配

### 浏览器兼容性
- ✅ Chrome/Edge (Android/iOS)
- ✅ Safari (iOS)
- ✅ Firefox (Android)
- ⚠️ 部分浏览器需HTTPS环境

### 响应式设计
- 自适应布局
- 触摸优化按钮
- 移动端录音权限处理

## 常见问题

### Q: 提示"麦克风权限被拒绝"
**A**: 在浏览器地址栏左侧,允许麦克风权限,然后刷新页面

### Q: 识别准确率低
**A**: 
- 确保环境安静
- 靠近麦克风说话
- 普通话识别准确率最高

### Q: 说话人区分不准确
**A**: 
- 确保说话人声音特征明显
- 避免声音重叠
- 讯飞盲分基于声纹,不保证100%准确

### Q: WebSocket连接失败
**A**:
- 确认后端服务已启动
- 检查端口8000是否被占用
- 查看浏览器控制台错误信息

### Q: 移动端无法录音
**A**:
- 确保使用HTTPS (本地开发localhost除外)
- 检查浏览器是否支持MediaDevices API
- 尝试更换浏览器

## 性能优化

### 音频数据传输
- 每帧4096样本 (约256ms)
- Base64编码增加约33%数据量
- 网络延迟约100-300ms

### 识别延迟
- 讯飞API响应: ~200ms
- 总延迟: ~500ms (可接受范围)

### 资源占用
- CPU: 中等 (音频处理)
- 内存: 约50MB
- 网络: 实时流传输

## 扩展功能

### 可添加功能
- [ ] 识别结果导出 (TXT/Word)
- [ ] 关键词高亮
- [ ] 实时字幕同步
- [ ] 语音情绪分析
- [ ] 多语言支持切换
- [ ] 录音文件上传识别

### 其他AI服务提供商
可通过修改配置切换到:
- 阿里云智能语音
- 腾讯云语音识别
- 百度AI语音
- Azure Speech Services

## 参考资料

- [讯飞语音听写API文档](https://www.xfyun.cn/doc/asr/voicedictation/API.html)
- [WebSocket协议规范](https://datatracker.ietf.org/doc/html/rfc6455)
- [MediaDevices API文档](https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices)
- [AudioContext API文档](https://developer.mozilla.org/en-US/docs/Web/API/AudioContext)

## 更新日志

### v1.0.0 (2025-11-17)
- ✅ 初版发布
- ✅ 实时语音识别
- ✅ 说话人盲分
- ✅ 移动端兼容
- ✅ 音量可视化

---

**开发团队**: AI工具导航项目组  
**技术支持**: 讯飞开放平台
