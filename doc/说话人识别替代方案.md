# 说话人识别替代方案

## 问题确认

**讯飞账号不支持说话人识别功能** - 返回数据中没有`rl`字段

## 临时解决方案

在讯飞功能未开通期间,提供3种替代方案:

---

## 方案A: VAD静音检测自动切换 ⭐推荐

### 原理

通过检测**静音时长**自动判断说话人切换:
- 说话 → 静音2秒 → 说话 = 可能是不同说话人
- 自动切换到下一个说话人ID

### 优点
- ✅ 完全自动,无需手动操作
- ✅ 多人连续对话效果较好
- ✅ 不依赖后端API

### 缺点
- ⚠️ 准确率约70-80%
- ⚠️ 同一人说话如果停顿太久会被误判

### 使用方法

**步骤1**: 在HTML中引入VAD检测脚本

修改 `before/html/voice-recognition.html`:

```html
<!-- 在原有JS之前添加 -->
<script src="../js/vad-speaker-detection.js"></script>
<script src="../js/voice-recognition.js"></script>
```

**步骤2**: 修改 `voice-recognition.js`

在`VoiceRecognitionApp`类中添加VAD检测器:

```javascript
class VoiceRecognitionApp {
    constructor() {
        // ... 原有代码 ...
        
        // ✨ 添加VAD说话人检测器
        this.vadDetector = new VADSpeakerDetector({
            silenceThreshold: 0.01,   // 音量阈值
            silenceDuration: 2000,    // 2秒静音切换说话人
            maxSpeakers: 4
        });
        
        this.currentSpeaker = 0;
    }
}
```

**步骤3**: 在音频处理中使用

修改`processor.onaudioprocess`:

```javascript
processor.onaudioprocess = (e) => {
    if (!this.isRecording) return;
    
    const inputData = e.inputBuffer.getChannelData(0);
    const sampleRate = e.inputBuffer.sampleRate;
    
    // ✨ VAD检测是否需要切换说话人
    const newSpeaker = this.vadDetector.update(inputData);
    if (newSpeaker !== null) {
        this.currentSpeaker = newSpeaker;
        console.log(`🔄 自动切换到说话人 ${newSpeaker + 1}`);
    }
    
    // 重采样
    let resampledData = inputData;
    if (sampleRate !== 16000) {
        resampledData = this.resampleAudio(inputData, sampleRate, 16000);
    }
    
    // 转换为PCM
    const pcmData = new Int16Array(resampledData.length);
    for (let i = 0; i < resampledData.length; i++) {
        const s = Math.max(-1, Math.min(1, resampledData[i]));
        pcmData[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
    }
    
    // Base64编码并发送
    const base64Audio = this.arrayBufferToBase64(pcmData.buffer);
    
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify({
            type: 'audio',
            audio: base64Audio,
            speaker: this.currentSpeaker  // ✨ 附带说话人ID
        }));
    }
};
```

**步骤4**: 修改后端处理

修改 `after/impl/voice_rec.py`:

```python
if data.get('type') == 'audio':
    audio_b64 = data['audio']
    speaker_from_frontend = data.get('speaker', None)  # ✨ 从前端获取
    
    # ... 发送给讯飞 ...
    
    # 接收讯飞结果后
    if full_text:
        await on_result_callback({
            'type': 'result',
            'text': full_text,
            'speaker': speaker_from_frontend,  # ✨ 使用前端提供的ID
            'is_final': data.get('status') == 2
        })
```

### 参数调优

根据实际效果调整:

```javascript
// 快速切换(适合快节奏对话)
silenceDuration: 1500  // 1.5秒

// 慢速切换(避免误判)
silenceDuration: 3000  // 3秒

// 调整音量阈值
silenceThreshold: 0.02  // 提高阈值,减少噪音干扰
```

---

## 方案B: 手动切换按钮 ⭐最准确

### 原理

界面上添加说话人切换按钮,说话人自己点击

### 优点
- ✅ 100%准确
- ✅ 用户可控
- ✅ 实现简单

### 缺点
- ⚠️ 需要手动操作
- ⚠️ 影响流畅度

### 实现方法

**步骤1**: 添加按钮到HTML

修改 `before/html/voice-recognition.html`:

```html
<!-- 在控制面板后添加 -->
<div class="speaker-selector">
    <h3>👥 当前说话人</h3>
    <div class="speaker-buttons">
        <button class="speaker-btn active" onclick="app.switchSpeaker(0)" id="speaker-btn-0">
            说话人1
        </button>
        <button class="speaker-btn" onclick="app.switchSpeaker(1)" id="speaker-btn-1">
            说话人2
        </button>
        <button class="speaker-btn" onclick="app.switchSpeaker(2)" id="speaker-btn-2">
            说话人3
        </button>
        <button class="speaker-btn" onclick="app.switchSpeaker(3)" id="speaker-btn-3">
            说话人4
        </button>
    </div>
</div>
```

**步骤2**: 添加CSS样式

```css
.speaker-selector {
    margin: 20px 0;
    text-align: center;
}

.speaker-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

.speaker-btn {
    padding: 10px 20px;
    border: 2px solid #667eea;
    background: transparent;
    color: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
}

.speaker-btn:hover {
    background: rgba(102, 126, 234, 0.2);
}

.speaker-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: scale(1.05);
}
```

**步骤3**: 添加切换函数

在`voice-recognition.js`中:

```javascript
/**
 * 手动切换说话人
 */
switchSpeaker(speakerId) {
    this.currentSpeaker = speakerId;
    
    // 更新按钮高亮
    document.querySelectorAll('.speaker-btn').forEach((btn, index) => {
        if (index === speakerId) {
            btn.classList.add('active');
        } else {
            btn.classList.remove('active');
        }
    });
    
    console.log(`👆 切换到说话人 ${speakerId + 1}`);
}
```

---

## 方案C: VAD + 手动结合 ⭐最佳实践

结合自动检测和手动切换的优点:

### 特性

- ✅ 自动检测为主
- ✅ 手动纠正为辅
- ✅ 灵活性强

### 实现

```javascript
// 同时启用VAD和手动按钮
class VoiceRecognitionApp {
    constructor() {
        // VAD自动检测
        this.vadDetector = new VADSpeakerDetector({
            silenceDuration: 2500,
            maxSpeakers: 4
        });
        
        // 手动切换优先级更高
        this.manualOverride = false;
        this.currentSpeaker = 0;
    }
    
    // 在音频处理中
    processor.onaudioprocess = (e) => {
        const inputData = e.inputBuffer.getChannelData(0);
        
        // 如果没有手动设置,使用VAD自动检测
        if (!this.manualOverride) {
            const newSpeaker = this.vadDetector.update(inputData);
            if (newSpeaker !== null) {
                this.currentSpeaker = newSpeaker;
                this.updateSpeakerUI(newSpeaker);
            }
        }
        
        // ... 发送音频数据 ...
    };
    
    // 手动切换
    switchSpeaker(speakerId) {
        this.currentSpeaker = speakerId;
        this.manualOverride = true;  // 启用手动模式
        this.updateSpeakerUI(speakerId);
        
        // 5秒后恢复自动检测
        setTimeout(() => {
            this.manualOverride = false;
        }, 5000);
    }
}
```

---

## 对比总结

| 方案 | 准确率 | 便利性 | 适用场景 |
|------|--------|--------|----------|
| VAD自动 | 70-80% | ⭐⭐⭐⭐⭐ | 多人轮流发言 |
| 手动按钮 | 100% | ⭐⭐ | 正式会议记录 |
| VAD+手动 | 85-95% | ⭐⭐⭐⭐ | 日常对话 |

---

## 最终建议

**短期** (1-2周):
- 使用"VAD + 手动结合"方案
- 用户体验最佳

**长期**:
- 联系讯飞开通真实的说话人识别功能
- 或切换到支持的服务商(阿里云/腾讯云)

---

*创建时间: 2025-11-17*
