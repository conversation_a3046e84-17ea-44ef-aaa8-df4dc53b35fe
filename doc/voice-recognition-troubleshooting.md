# 语音识别故障排查指南

## 错误码 10165: invalid handle

### 问题描述
```
❌ 识别错误: invalid handle (code: 10165)
```

### 可能原因

#### 1. 服务未开通 ⭐ 最常见
**症状**: 连接成功但首帧返回10165  
**原因**: 讯飞控制台未开通"语音听写(流式版)"服务

**解决方案**:
1. 登录 [讯飞开放平台](https://www.xfyun.cn/)
2. 进入 **控制台** → **我的应用**
3. 找到APPID `4ff6d5e8` 对应的应用
4. 点击 **服务管理**
5. 确认已开通 **"语音听写(流式版)"** 服务
6. 如未开通,点击 **"添加服务"** 开通

![](https://www.xfyun.cn/doc/assets/img/iat_service.png)

#### 2. AppID配置错误
**症状**: 鉴权通过但业务失败  
**检查项**:
- 确认`config.json`中的`app_id`与控制台一致
- 注意复制时有无多余空格

当前配置:
```json
{
  "app_id": "4ff6d5e8"
}
```

#### 3. API密钥错误
**症状**: HTTP 401或10165  
**检查项**:
- `api_key`是否正确
- `api_secret`是否正确
- 密钥是否从正确的应用复制

#### 4. 业务参数不匹配
**症状**: 识别失败  
**检查项**:

```python
# 当前配置
{
  "domain": "iat",      # 必须是iat
  "language": "zh_cn",  # 支持: zh_cn, en_us等
  "accent": "mandarin", # 普通话: mandarin
  "vinfo": 1,           # 说话人识别: 0或1
  "vad_eos": 10000      # 静音检测: 毫秒
}
```

### 快速诊断

运行诊断脚本:
```bash
cd d:\private\private01\after
python test_xfyun_auth.py
```

查看输出:
- ✅ 连接成功 → 鉴权OK
- ✅ 发送成功 → 协议OK
- ❌ 服务器响应错误 → 检查服务开通状态

### 手动验证步骤

#### Step 1: 检查服务开通
1. 访问讯飞控制台
2. 查看应用服务列表
3. 确认"语音听写(流式版)"已开通且生效

#### Step 2: 验证凭证
```bash
APPID: 4ff6d5e8
APIKey: a2d8619f4d0d918d61957bd647fe0c7b
APISecret: OWU1ZjMyMDM2ZmNiMjllYTg1OTM2ZTQ4
```

在控制台 → 应用详情页核对是否一致

#### Step 3: 测试基础连接
```python
# 查看后端日志
# 应该看到:
📡 已连接到讯飞语音识别服务  # ✅ 连接成功
📤 发送第一帧 (app_id: 4ff6d5e8)  # ✅ 发送成功

# 如果看到:
❌ 识别错误: invalid handle (code: 10165)  # ⚠️ 服务未开通
```

### 其他常见错误

#### 错误码 10163: 非法参数
**原因**: 业务参数格式错误  
**解决**: 检查`language`, `domain`, `accent`等参数

#### 错误码 401: 鉴权失败
**原因**: 签名计算错误或密钥错误  
**解决**: 检查`api_key`和`api_secret`,确保签名算法正确

#### 错误码 10160: 应用不存在
**原因**: `app_id`错误  
**解决**: 重新复制正确的APPID

#### 连接超时
**原因**: 网络问题或防火墙  
**解决**: 
- 检查网络连接
- 确认可以访问`ws-api.xfyun.cn`
- 检查企业防火墙设置

### 完整诊断清单

- [ ] 讯飞控制台已登录
- [ ] 应用已创建(APPID: 4ff6d5e8)
- [ ] "语音听写(流式版)"服务已开通
- [ ] API凭证已正确配置
- [ ] 后端服务正常启动
- [ ] WebSocket连接成功
- [ ] 前端录音权限已授权
- [ ] 音频数据正常发送

### 联系支持

如以上方法仍无法解决:

1. **讯飞官方技术支持**
   - 工单系统: [提交工单](https://www.xfyun.cn/services/online_services)
   - 技术论坛: [讯飞开发者论坛](http://bbs.xfyun.cn/)
   - 咨询QQ群: 参见官网

2. **提供的信息**
   - APPID: 4ff6d5e8
   - 错误码: 10165
   - 完整错误信息
   - 后端日志输出
   - 测试脚本运行结果

### 成功标志

当一切正常时,你应该看到:
```
📡 已连接到讯飞语音识别服务
📤 发送第一帧 (app_id: 4ff6d5e8)
📋 业务参数: domain=iat, language=zh_cn, accent=mandarin
✅ 识别成功: {"type":"result","text":"你好","speaker":0,"is_final":false}
```

---

**最可能的原因**: 讯飞控制台未开通"语音听写(流式版)"服务!
