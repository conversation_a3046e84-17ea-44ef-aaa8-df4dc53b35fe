# 服务启动指南

## 快速启动

### 方式1: 使用启动脚本(推荐) ⭐

#### Windows用户
双击运行: `d:/private/private01/after/start.bat`

#### 或使用PowerShell
```powershell
cd d:/private/private01/after
python main.py
```

### 方式2: 使用uvicorn命令行

```powershell
cd d:/private/private01/after
uvicorn main:app --host 127.0.0.1 --port 8000
```

---

## 启动成功标志

看到以下输出说明启动成功:
```
==================================================
AI工具导航后端服务
==================================================
服务地址: http://localhost:8000
API文档: http://localhost:8000/docs
==================================================
提示: 按 Ctrl+C 停止服务
==================================================
INFO:     Started server process [进程ID]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
```

**关键**: 必须看到 `Uvicorn running on http://127.0.0.1:8000` 这行!

---

## 验证服务是否正常

### 1. 浏览器测试
打开浏览器访问:
```
http://localhost:8000/api/health
```

应该看到:
```json
{"status":"healthy"}
```

### 2. 查看API文档
```
http://localhost:8000/docs
```

应该看到Swagger交互式文档界面。

### 3. 测试图片生成接口
在Swagger文档中:
1. 找到 `POST /api/image-generation`
2. 点击 "Try it out"
3. 输入测试数据:
```json
{
  "prompt": "一只可爱的猫",
  "width": 2048,
  "height": 2048
}
```
4. 点击 "Execute"
5. 查看响应结果

---

## 常见启动问题

### 问题1: 端口被占用

**错误信息**:
```
Error: [Errno 10048] error while attempting to bind on address ('127.0.0.1', 8000)
```

**解决方案**:
```powershell
# 查看占用8000端口的进程
netstat -ano | findstr :8000

# 结束进程(替换PID为实际的进程ID)
taskkill /F /PID <PID>

# 或修改端口号
# 编辑 after/config/config.json
# 将 "port": 8000 改为 8001
```

### 问题2: 缺少依赖

**错误信息**:
```
ModuleNotFoundError: No module named 'fastapi'
```

**解决方案**:
```powershell
cd d:/private/private01/after
pip install -r requirements.txt
```

### 问题3: 配置文件错误

**错误信息**:
```
json.decoder.JSONDecodeError
```

**解决方案**:
检查 `after/config/config.json` 语法是否正确,特别注意:
- 逗号位置
- 引号配对
- 大括号配对

### 问题4: 启动后没有看到 "Uvicorn running"

**原因**: `reload=True` 模式需要使用字符串导入路径

**解决方案**:
已在代码中修复,使用 `"main:app"` 而不是 `app` 对象。

如果还是有问题,临时关闭reload:
```python
# 编辑 main.py
uvicorn.run(
    "main:app",
    host="127.0.0.1",
    port=8000,
    reload=False  # 关闭热重载
)
```

---

## 停止服务

在运行服务的终端窗口中按 `Ctrl+C`

---

## 开发模式 vs 生产模式

### 开发模式(当前配置)
```json
{
  "debug": false,
  "cors_origins": ["*"]
}
```
- 允许所有来源访问(CORS)
- 不使用热重载(稳定)
- 适合开发调试

### 生产模式(未来部署)
```json
{
  "debug": false,
  "cors_origins": ["https://yourdomain.com"]
}
```
- 限制允许的域名
- 关闭调试模式
- 使用生产级ASGI服务器(如Gunicorn + Uvicorn)

---

## 日志查看

服务运行时会在终端输出详细日志:
- API请求信息
- 火山AI调用过程
- 错误信息

**建议**: 保持终端窗口打开,方便查看实时日志。

---

## 后台运行(可选)

### Windows
```powershell
# 使用start命令在新窗口运行
start python main.py

# 或创建Windows服务(高级)
```

### Linux/Mac
```bash
# 使用nohup后台运行
nohup python main.py > server.log 2>&1 &

# 或使用screen/tmux
screen -S api-server
python main.py
# 按 Ctrl+A, D 分离会话
```

---

## 性能优化

### 使用Gunicorn(生产环境推荐)
```bash
pip install gunicorn
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 127.0.0.1:8000
```

参数说明:
- `-w 4`: 4个工作进程
- `-k uvicorn.workers.UvicornWorker`: 使用Uvicorn worker
- `--bind`: 绑定地址和端口

---

## 监控和维护

### 健康检查脚本
创建 `check_health.py`:
```python
import requests
import time

while True:
    try:
        response = requests.get('http://localhost:8000/api/health', timeout=5)
        if response.status_code == 200:
            print(f"✅ [{time.strftime('%Y-%m-%d %H:%M:%S')}] 服务正常")
        else:
            print(f"❌ [{time.strftime('%Y-%m-%d %H:%M:%S')}] 服务异常: {response.status_code}")
    except Exception as e:
        print(f"❌ [{time.strftime('%Y-%m-%d %H:%M:%S')}] 连接失败: {e}")
    
    time.sleep(60)  # 每分钟检查一次
```

---

## 故障恢复

如果服务崩溃,可以使用自动重启脚本:

### restart.bat (Windows)
```batch
@echo off
:start
echo 启动服务...
python main.py
echo 服务已停止,5秒后重启...
timeout /t 5
goto start
```

---

## 技术支持

如果遇到无法解决的启动问题:
1. 查看完整错误堆栈
2. 检查Python版本(需要3.8+)
3. 检查依赖版本是否兼容
4. 提供详细的错误日志
