@echo off
chcp 65001 >nul
echo.
echo ========================================
echo   AI工具导航 - 项目启动器
echo ========================================
echo.
echo 请选择要启动的服务:
echo.
echo [1] 启动后端服务 (端口: 8000)
echo [2] 启动前端服务 (端口: 3000)
echo [3] 同时启动前后端 (推荐)
echo [4] 退出
echo.
echo ========================================
set /p choice=请输入选项 (1-4): 

if "%choice%"=="1" goto backend
if "%choice%"=="2" goto frontend
if "%choice%"=="3" goto both
if "%choice%"=="4" goto end
goto invalid

:backend
echo.
echo 正在启动后端服务...
cd /d "%~dp0after"
start "AI工具导航-后端" cmd /k "python main.py"
echo.
echo ✓ 后端服务已在新窗口启动
echo   访问: http://localhost:8000/docs
timeout /t 2 >nul
goto end

:frontend
echo.
echo 正在启动前端服务...
cd /d "%~dp0before"
start "AI工具导航-前端" cmd /k "python -m http.server 3000"
echo.
echo ✓ 前端服务已在新窗口启动
echo   访问: http://localhost:3000/html/
timeout /t 2 >nul
goto end

:both
echo.
echo 正在同时启动前后端服务...
echo.
echo [1/2] 启动后端服务...
cd /d "%~dp0after"
start "AI工具导航-后端" cmd /k "python main.py"
timeout /t 2 >nul

echo [2/2] 启动前端服务...
cd /d "%~dp0before"
start "AI工具导航-前端" cmd /k "python -m http.server 3000"
timeout /t 2 >nul

echo.
echo ========================================
echo ✓ 前后端服务已全部启动！
echo ========================================
echo.
echo 前端: http://localhost:3000/html/
echo 后端: http://localhost:8000
echo API文档: http://localhost:8000/docs
echo.
echo 提示: 请保持两个服务窗口打开
echo       按 Ctrl+C 可停止相应服务
echo.
timeout /t 3 >nul
start http://localhost:3000/html/
goto end

:invalid
echo.
echo ❌ 无效选项，请重新运行脚本
timeout /t 2 >nul
goto end

:end
echo.
pause
