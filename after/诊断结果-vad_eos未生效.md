# vad_eos参数未生效诊断报告

## 问题现象
配置了`vad_eos: 4000`(4秒),但实际测试时断句速度感觉没有变化,仍然约0.5-1秒就断句。

## 诊断结果

### ✅ 配置传递正常
- `config.json`中已配置`vad_eos: 4000`
- 后端代码成功读取配置值:`self.vad_eos = 4000`
- WebSocket URL中已包含参数:`vad_eos=4000`
- 参数值传递链路完整

### ❌ **根本原因: 新版API可能不支持该参数**

你们使用的是讯飞**实时转写+大模型新版API**:
```
wss://office-api-ast-dx.iflyaisol.com/ast/communicate/v1
```

而`vad_eos`参数是**旧版语音听写API**的参数:
```
wss://ws-api.xfyun.cn/v2/iat
```

## API版本差异对比

| 特性 | 旧版API (v2/iat) | 新版API (ast/communicate/v1) |
|-----|----------------|--------------------------|
| 端点 | ws-api.xfyun.cn/v2/iat | office-api-ast-dx.iflyaisol.com |
| 鉴权方式 | APIKey + APISecret | AccessKeyId + AccessKeySecret |
| 参数传递 | BusinessArgs字典 | URL Query参数 |
| 说话人分离 | vinfo=1 (基础) | role_type=2 (智能盲分) |
| vad_eos | ✅ 支持 | ❓ 未知 |
| 功能定位 | 语音听写 | 实时转写+大模型 |

## 可能的解决方案

### 方案1: 确认新版API支持的断句参数

新版API可能使用不同的参数名称,常见候选:

1. **punc_timeout** - 标点符号超时(毫秒)
2. **max_silence** - 最大静音时长(毫秒)
3. **vad_timeout** - VAD超时(毫秒)
4. **silence_duration** - 静音持续时间(毫秒)

**建议操作:**
1. 查阅讯飞开放平台最新文档:
   - [讯飞开放平台控制台](https://console.xfyun.cn/)
   - 查找"实时转写+大模型"或"AST API"的API文档
   - 查看完整参数列表

2. 联系讯飞技术支持确认:
   - 新版API是否支持自定义断句时长
   - 正确的参数名称和取值范围

### 方案2: 降级使用旧版API(不推荐)

如果确实需要`vad_eos`功能,可以切换回旧版语音听写API,但会失去:
- 智能说话人盲分功能(role_type=2)
- 大模型优化的识别效果
- 更好的多人对话场景支持

### 方案3: 前端处理断句逻辑(临时方案)

在前端合并短句,模拟延长断句时间:

```javascript
class VoiceTranscriber {
    constructor() {
        this.pendingResults = [];  // 待合并的结果
        this.mergeTimer = null;
        this.MERGE_TIMEOUT = 4000;  // 4秒内的结果合并为一句
    }
    
    handleResult(data) {
        if (!data.is_final) return;  // 只处理最终结果
        
        clearTimeout(this.mergeTimer);
        this.pendingResults.push(data.text);
        
        // 4秒内没有新结果,认为句子完整
        this.mergeTimer = setTimeout(() => {
            const fullText = this.pendingResults.join('');
            this.displayResult(fullText);  // 显示合并后的句子
            this.pendingResults = [];
        }, this.MERGE_TIMEOUT);
    }
}
```

**优点**: 不依赖API支持,可以自定义合并逻辑  
**缺点**: 会延迟显示,实时性下降

## 立即可执行的验证步骤

### 步骤1: 查看后端实时日志

启动后端并开始录音,观察讯飞返回的原始数据:

```bash
cd d:/work/aiCode/Private/private_ai_navigation/after
python main.py
```

在日志中查找:
1. 是否有关于`vad_eos`的警告或错误
2. 讯飞返回的`seg_id`变化频率(判断实际断句间隔)

### 步骤2: 抓包分析(高级)

使用Wireshark或浏览器开发者工具:
1. 捕获WebSocket通信
2. 查看建立连接时的URL参数
3. 确认讯飞是否真的收到`vad_eos=4000`

### 步骤3: 对比测试

分别测试不同`vad_eos`值:
- 500ms (默认值)
- 2000ms (2秒)
- 4000ms (4秒)
- 10000ms (10秒)

如果所有值表现一致,说明参数确实未生效。

## 临时建议

在确认新版API是否支持之前:

1. **接受当前断句速度**: 如果0.5-1秒的断句对业务影响不大,可以暂时使用
2. **调整说话习惯**: 需要连续说话时,减少停顿时长
3. **使用前端合并**: 实现方案3的前端合并逻辑

## 下一步行动

### 优先级1(立即执行)
- [ ] 查阅讯飞开放平台控制台的API文档
- [ ] 在"实时转写+大模型"服务页面找参数说明
- [ ] 确认新版API的完整参数列表

### 优先级2(如果P1无果)
- [ ] 提交讯飞工单询问断句参数
- [ ] 或拨打讯飞技术支持电话: 400-9696-968

### 优先级3(备选方案)
- [ ] 实现前端合并逻辑(方案3)
- [ ] 评估是否切换回旧版API

## 联系信息

- **讯飞开放平台**: https://www.xfyun.cn/
- **文档中心**: https://www.xfyun.cn/doc/
- **工单系统**: 登录控制台 → 帮助中心 → 提交工单
- **电话支持**: 400-9696-968

---

**诊断时间**: 2025-11-21  
**诊断工具**: `after/test_vad_eos.py`  
**结论**: 参数传递正常,但新版API可能不支持`vad_eos`参数
