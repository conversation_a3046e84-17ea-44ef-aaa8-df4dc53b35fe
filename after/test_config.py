"""测试配置加载"""
import json
from pathlib import Path

config_path = Path(__file__).parent / 'config/config.json'
with open(config_path, 'r', encoding='utf-8') as f:
    config = json.load(f)

print("=" * 50)
print("配置测试")
print("=" * 50)

# 测试访问voiceprint配置
try:
    voiceprint_config = config['ai_providers']['voiceprint']
    print("\n✅ 声纹配置加载成功:")
    print(f"  - app_id: {voiceprint_config['app_id']}")
    print(f"  - group_id: {voiceprint_config['group_id']}")
    print(f"  - match_threshold: {voiceprint_config['match_threshold']}")
except KeyError as e:
    print(f"\n❌ 配置路径错误: {e}")

# 测试VoiceprintClient初始化
try:
    from impl.voiceprint import VoiceprintClient
    client = VoiceprintClient()
    print("\n✅ VoiceprintClient 初始化成功")
except Exception as e:
    print(f"\n❌ VoiceprintClient 初始化失败: {e}")

# 测试VoiceprintDB初始化
try:
    from impl.voiceprint_db import VoiceprintDB
    db = VoiceprintDB()
    print("✅ VoiceprintDB 初始化成功")
except Exception as e:
    print(f"❌ VoiceprintDB 初始化失败: {e}")

print("\n" + "=" * 50)
