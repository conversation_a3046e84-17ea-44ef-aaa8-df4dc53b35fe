"""
测试vad_eos参数是否生效
对比旧版API和新版API的参数差异
"""
import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

from impl.voice_rec import XFVoiceRecognizer

def main():
    print("=" * 60)
    print("讯飞API参数诊断")
    print("=" * 60)
    
    recognizer = XFVoiceRecognizer()
    
    print(f"\n【配置信息】")
    print(f"  APPID: {recognizer.app_id}")
    print(f"  API地址: {recognizer.base_url}")
    print(f"  vad_eos配置: {recognizer.vad_eos}ms")
    
    print(f"\n【URL参数】")
    url = recognizer.create_url()
    
    # 检查URL中是否包含vad_eos
    if 'vad_eos' in url:
        # 提取vad_eos参数值
        import re
        match = re.search(r'vad_eos[=%](\d+)', url)
        if match:
            url_vad_value = match.group(1)
            print(f"  ✅ URL中包含vad_eos参数")
            print(f"  📊 URL中的vad_eos值: {url_vad_value}")
            print(f"  🔍 配置值 vs URL值: {recognizer.vad_eos} vs {url_vad_value}")
            if str(recognizer.vad_eos) == url_vad_value:
                print(f"  ✅ 参数传递正确")
            else:
                print(f"  ❌ 参数值不匹配!")
        else:
            print(f"  ⚠️ URL中找到vad_eos但无法解析值")
    else:
        print(f"  ❌ URL中未包含vad_eos参数")
    
    print(f"\n【API版本对比】")
    print(f"  旧版API: wss://ws-api.xfyun.cn/v2/iat")
    print(f"    - 参数格式: BusinessArgs字典")
    print(f"    - 支持vad_eos: 是")
    print(f"  ")
    print(f"  当前API: {recognizer.base_url}")
    print(f"    - 参数格式: URL Query参数")
    print(f"    - 支持vad_eos: 未知 (需测试验证)")
    
    print(f"\n【诊断建议】")
    print(f"  1. 新版API可能不支持vad_eos参数")
    print(f"  2. 参数名称可能已改变,常见替代名:")
    print(f"     - max_silence: 最大静音时长")
    print(f"     - punc_timeout: 标点超时")
    print(f"     - vad_timeout: VAD超时")
    print(f"  3. 建议查阅讯飞新版API文档确认支持的参数")
    
    print(f"\n【测试方法】")
    print(f"  1. 启动后端服务并开始录音")
    print(f"  2. 说话时停顿{recognizer.vad_eos/1000}秒")
    print(f"  3. 观察后端日志中的讯飞返回数据")
    print(f"  4. 查看是否在{recognizer.vad_eos/1000}秒后才断句")
    
    print("\n" + "=" * 60)

if __name__ == '__main__':
    main()
