"""
声纹数据库模型
使用SQLite存储声纹ID与用户姓名的映射关系
"""
import sqlite3
from pathlib import Path
from datetime import datetime
from typing import List, Optional, Dict


class VoiceprintDB:
    """声纹数据库管理类"""
    
    def __init__(self, db_path='data/voiceprints.db'):
        """
        初始化数据库连接
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = Path(__file__).parent.parent / db_path
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self._init_database()
    
    def _init_database(self):
        """创建数据库表（如果不存在）"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS voiceprints (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                feature_id TEXT UNIQUE NOT NULL,
                user_name TEXT NOT NULL,
                group_id TEXT NOT NULL,
                feature_info TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建索引
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_feature_id 
            ON voiceprints(feature_id)
        ''')
        
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_group_id 
            ON voiceprints(group_id)
        ''')
        
        conn.commit()
        conn.close()
    
    def add_voiceprint(self, feature_id: str, user_name: str, group_id: str, feature_info: str = "") -> bool:
        """
        添加声纹记录
        
        Args:
            feature_id: 特征ID（讯飞返回）
            user_name: 用户姓名
            group_id: 分组ID
            feature_info: 特征描述
            
        Returns:
            bool: 是否成功
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO voiceprints (feature_id, user_name, group_id, feature_info)
                VALUES (?, ?, ?, ?)
            ''', (feature_id, user_name, group_id, feature_info))
            
            conn.commit()
            conn.close()
            return True
        except sqlite3.IntegrityError:
            return False
        except Exception as e:
            print(f"数据库错误: {e}")
            return False
    
    def get_voiceprint_by_feature_id(self, feature_id: str) -> Optional[Dict]:
        """
        根据特征ID查询声纹信息
        
        Args:
            feature_id: 特征ID
            
        Returns:
            dict or None: 声纹信息
        """
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM voiceprints WHERE feature_id = ?
        ''', (feature_id,))
        
        row = cursor.fetchone()
        conn.close()
        
        if row:
            return dict(row)
        return None
    
    def get_all_voiceprints(self, group_id: str = "meeting_group") -> List[Dict]:
        """
        获取所有声纹记录
        
        Args:
            group_id: 分组ID
            
        Returns:
            list: 声纹列表
        """
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM voiceprints 
            WHERE group_id = ?
            ORDER BY created_at DESC
        ''', (group_id,))
        
        rows = cursor.fetchall()
        conn.close()
        
        return [dict(row) for row in rows]
    
    def delete_voiceprint(self, feature_id: str) -> bool:
        """
        删除声纹记录
        
        Args:
            feature_id: 特征ID
            
        Returns:
            bool: 是否成功
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                DELETE FROM voiceprints WHERE feature_id = ?
            ''', (feature_id,))
            
            conn.commit()
            affected = cursor.rowcount
            conn.close()
            
            return affected > 0
        except Exception as e:
            print(f"数据库错误: {e}")
            return False
    
    def get_user_name(self, feature_id: str) -> Optional[str]:
        """
        根据特征ID获取用户姓名（常用查询）
        
        Args:
            feature_id: 特征ID
            
        Returns:
            str or None: 用户姓名
        """
        voiceprint = self.get_voiceprint_by_feature_id(feature_id)
        return voiceprint['user_name'] if voiceprint else None


# 测试代码
if __name__ == '__main__':
    db = VoiceprintDB()
    
    print("=" * 50)
    print("声纹数据库测试")
    print("=" * 50)
    
    # 测试添加
    print("\n1. 添加声纹记录...")
    success = db.add_voiceprint(
        feature_id="test_user_001",
        user_name="张三",
        group_id="meeting_group",
        feature_info="测试用户"
    )
    print(f"结果: {'成功' if success else '失败'}")
    
    # 测试查询
    print("\n2. 查询声纹记录...")
    record = db.get_voiceprint_by_feature_id("test_user_001")
    print(f"结果: {record}")
    
    # 测试获取所有
    print("\n3. 获取所有声纹...")
    all_records = db.get_all_voiceprints()
    print(f"共 {len(all_records)} 条记录")
    
    print("\n✅ 数据库功能正常")
