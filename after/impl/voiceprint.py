"""
讯飞声纹识别API封装
实现声纹特征库管理、声纹注册、1:1和1:N比对等功能
"""
import json
import base64
import hmac
import hashlib
from datetime import datetime, timezone
from urllib.parse import urlencode
import requests
from pathlib import Path


class VoiceprintClient:
    """讯飞声纹识别客户端"""
    
    API_URL = "https://api.xf-yun.com/v1/private/s782b4996"
    
    def __init__(self, app_id=None, api_key=None, api_secret=None, config_path='config/config.json'):
        """
        初始化声纹客户端
        
        Args:
            app_id: 讯飞AppID
            api_key: APIKey
            api_secret: APISecret
            config_path: 配置文件路径（如果不提供凭证则从配置读取）
        """
        if app_id and api_key and api_secret:
            self.app_id = app_id
            self.api_key = api_key
            self.api_secret = api_secret
        else:
            # 从配置文件读取
            config_file = Path(__file__).parent.parent / config_path
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # voiceprint配置在ai_providers下
                voiceprint_config = config.get('ai_providers', {}).get('voiceprint', {})
                self.app_id = voiceprint_config.get('app_id')
                self.api_key = voiceprint_config.get('api_key')
                self.api_secret = voiceprint_config.get('api_secret')
        
        if not all([self.app_id, self.api_key, self.api_secret]):
            raise ValueError("缺少讯飞声纹API凭证")
    
    def _generate_signature(self):
        """
        生成HMAC-SHA256签名
        
        Returns:
            tuple: (authorization, host, date)
        """
        # 生成RFC1123格式的时间戳
        now = datetime.now(timezone.utc)
        date = now.strftime("%a, %d %b %Y %H:%M:%S GMT")
        
        # 构建signature_origin
        host = "api.xf-yun.com"
        request_line = "POST /v1/private/s782b4996 HTTP/1.1"
        signature_origin = f"host: {host}\ndate: {date}\n{request_line}"
        
        # 计算HMAC-SHA256签名
        signature_sha = hmac.new(
            self.api_secret.encode('utf-8'),
            signature_origin.encode('utf-8'),
            hashlib.sha256
        ).digest()
        
        # Base64编码
        signature = base64.b64encode(signature_sha).decode('utf-8')
        
        # 构建authorization_origin
        authorization_origin = (
            f'api_key="{self.api_key}", '
            f'algorithm="hmac-sha256", '
            f'headers="host date request-line", '
            f'signature="{signature}"'
        )
        
        # Base64编码authorization
        authorization = base64.b64encode(authorization_origin.encode('utf-8')).decode('utf-8')
        
        return authorization, host, date
    
    def _make_request(self, payload):
        """
        发送HTTP请求到讯飞API
        
        Args:
            payload: 请求体（dict）
            
        Returns:
            dict: API响应结果
        """
        # 生成签名
        authorization, host, date = self._generate_signature()
        
        # 构建请求URL
        params = {
            'authorization': authorization,
            'host': host,
            'date': date
        }
        url = f"{self.API_URL}?{urlencode(params)}"
        
        # 发送请求
        headers = {
            'Content-Type': 'application/json'
        }
        
        try:
            print(f"🌐 发送讯飞API请求...")
            print(f"   URL: {self.API_URL}")
            print(f"   Payload keys: {list(payload.keys())}")
            
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            
            print(f"   HTTP Status: {response.status_code}")
            print(f"   Response: {response.text[:500]}")
            
            # 不管HTTP状态码,都解析JSON响应
            response_data = response.json()
            return response_data
            
        except requests.exceptions.RequestException as e:
            print(f"❌ API请求异常: {e}")
            return {
                'header': {'code': -1, 'message': f'API请求失败: {str(e)}'}
            }
    
    def create_group(self, group_id, group_name="", group_info=""):
        """
        创建声纹特征库
        
        Args:
            group_id: 分组ID（字母数字下划线，最长32）
            group_name: 分组名称
            group_info: 分组描述
            
        Returns:
            dict: {success, data/message}
        """
        payload = {
            "header": {
                "app_id": self.app_id,
                "status": 3
            },
            "parameter": {
                "s782b4996": {
                    "func": "createGroup",
                    "groupId": group_id,
                    "groupName": group_name,
                    "groupInfo": group_info,
                    "createGroupRes": {
                        "encoding": "utf8",
                        "compress": "raw",
                        "format": "json"
                    }
                }
            }
        }
        
        result = self._make_request(payload)
        
        # 解析响应
        if result.get('header', {}).get('code') == 0:
            text_b64 = result.get('payload', {}).get('createGroupRes', {}).get('text', '')
            if text_b64:
                data = json.loads(base64.b64decode(text_b64))
                return {'success': True, 'data': data}
            return {'success': False, 'message': '响应数据为空'}
        else:
            return {
                'success': False,
                'message': result.get('header', {}).get('message', '未知错误')
            }
    
    def create_feature(self, group_id, feature_id, audio_base64, feature_info=""):
        """
        添加音频特征（录入声纹）
        
        Args:
            group_id: 分组ID
            feature_id: 特征ID（唯一标识）
            audio_base64: 音频数据（Base64编码的MP3，16k采样率）
            feature_info: 特征描述（建议包含用户名和时间戳）
            
        Returns:
            dict: {success, data/message}
        """
        payload = {
            "header": {
                "app_id": self.app_id,
                "status": 3
            },
            "parameter": {
                "s782b4996": {
                    "func": "createFeature",
                    "groupId": group_id,
                    "featureId": feature_id,
                    "featureInfo": feature_info,
                    "createFeatureRes": {
                        "encoding": "utf8",
                        "compress": "raw",
                        "format": "json"
                    }
                }
            },
            "payload": {
                "resource": {
                    "encoding": "lame",
                    "sample_rate": 16000,
                    "channels": 1,
                    "bit_depth": 16,
                    "status": 3,
                    "audio": audio_base64
                }
            }
        }
        
        result = self._make_request(payload)
        
        # 解析响应
        if result.get('header', {}).get('code') == 0:
            text_b64 = result.get('payload', {}).get('createFeatureRes', {}).get('text', '')
            if text_b64:
                data = json.loads(base64.b64decode(text_b64))
                return {'success': True, 'data': data}
            return {'success': False, 'message': '响应数据为空'}
        else:
            return {
                'success': False,
                'message': result.get('header', {}).get('message', '未知错误')
            }
    
    def query_feature_list(self, group_id):
        """
        查询声纹特征列表
        
        Args:
            group_id: 分组ID
            
        Returns:
            dict: {success, data: [{featureId, featureInfo}, ...]}
        """
        payload = {
            "header": {
                "app_id": self.app_id,
                "status": 3
            },
            "parameter": {
                "s782b4996": {
                    "func": "queryFeatureList",
                    "groupId": group_id,
                    "queryFeatureListRes": {
                        "encoding": "utf8",
                        "compress": "raw",
                        "format": "json"
                    }
                }
            }
        }
        
        result = self._make_request(payload)
        
        # 解析响应
        if result.get('header', {}).get('code') == 0:
            text_b64 = result.get('payload', {}).get('queryFeatureListRes', {}).get('text', '')
            if text_b64:
                data = json.loads(base64.b64decode(text_b64))
                return {'success': True, 'data': data}
            return {'success': True, 'data': []}
        else:
            return {
                'success': False,
                'message': result.get('header', {}).get('message', '未知错误')
            }
    
    def search_feature_1n(self, group_id, audio_base64, top_k=5):
        """
        声纹比对 1:N（在声纹库中搜索最相似的特征）
        
        Args:
            group_id: 分组ID
            audio_base64: 待匹配的音频（Base64编码）
            top_k: 返回最相似的前K个结果（最大10）
            
        Returns:
            dict: {success, data: {scoreList: [{score, featureId, featureInfo}, ...]}}
        """
        payload = {
            "header": {
                "app_id": self.app_id,
                "status": 3
            },
            "parameter": {
                "s782b4996": {
                    "func": "searchFea",
                    "groupId": group_id,
                    "topK": min(top_k, 10),
                    "searchFeaRes": {
                        "encoding": "utf8",
                        "compress": "raw",
                        "format": "json"
                    }
                }
            },
            "payload": {
                "resource": {
                    "encoding": "lame",
                    "sample_rate": 16000,
                    "channels": 1,
                    "bit_depth": 16,
                    "status": 3,
                    "audio": audio_base64
                }
            }
        }
        
        result = self._make_request(payload)
        
        # 解析响应
        if result.get('header', {}).get('code') == 0:
            text_b64 = result.get('payload', {}).get('searchFeaRes', {}).get('text', '')
            if text_b64:
                data = json.loads(base64.b64decode(text_b64))
                return {'success': True, 'data': data}
            return {'success': False, 'message': '响应数据为空'}
        else:
            return {
                'success': False,
                'message': result.get('header', {}).get('message', '未知错误')
            }
    
    def delete_feature(self, group_id, feature_id):
        """
        删除指定声纹特征
        
        Args:
            group_id: 分组ID
            feature_id: 特征ID
            
        Returns:
            dict: {success, message}
        """
        payload = {
            "header": {
                "app_id": self.app_id,
                "status": 3
            },
            "parameter": {
                "s782b4996": {
                    "func": "deleteFeature",
                    "groupId": group_id,
                    "featureId": feature_id,
                    "deleteFeatureRes": {
                        "encoding": "utf8",
                        "compress": "raw",
                        "format": "json"
                    }
                }
            }
        }
        
        result = self._make_request(payload)
        
        # 解析响应
        if result.get('header', {}).get('code') == 0:
            text_b64 = result.get('payload', {}).get('deleteFeatureRes', {}).get('text', '')
            if text_b64:
                data = json.loads(base64.b64decode(text_b64))
                # 检查返回的featureId是否匹配(讯飞API成功返回featureId而不是msg)
                if data.get('featureId') == feature_id:
                    return {'success': True, 'message': '删除成功'}
                elif data.get('msg') == 'success':  # 兼容旧版本API响应
                    return {'success': True, 'message': '删除成功'}
                else:
                    return {'success': False, 'message': f'删除失败: 返回数据异常 {data}'}
            return {'success': False, 'message': '删除失败: 响应数据为空'}
        else:
            return {
                'success': False,
                'message': result.get('header', {}).get('message', '未知错误')
            }


# 测试代码
if __name__ == '__main__':
    # 使用提供的凭证进行测试
    client = VoiceprintClient(
        app_id="4ff6d5e8",
        api_key="a2d8619f4d0d918d61957bd647fe0c7b",
        api_secret="OWU1ZjMyMDM2ZmNiMjllYTg1OTM2ZTQ4"
    )
    
    print("=" * 50)
    print("讯飞声纹API测试")
    print("=" * 50)
    
    # 测试创建特征库
    print("\n1. 创建声纹特征库...")
    result = client.create_group(
        group_id="test_meeting_group",
        group_name="测试会议声纹库",
        group_info="用于测试声纹识别功能"
    )
    print(f"结果: {result}")
    
    print("\n✅ 声纹客户端初始化成功")
