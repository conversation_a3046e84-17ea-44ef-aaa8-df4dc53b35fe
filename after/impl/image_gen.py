"""
图片生成模块
负责处理AI图片生成相关的业务逻辑
对接火山引擎豆包AI图片生成API
"""
import json
import time
import requests
from pathlib import Path
from typing import Optional, List, Dict, Any


class ImageGenerator:
    """图片生成器类 - 对接火山引擎豆包SeedreamV4模型"""
    
    def __init__(self, config_path='../config/config.json'):
        """初始化图片生成器"""
        self.config = self._load_config(config_path)
        
        # 获取当前激活的提供商
        active_provider = self.config.get('active_providers', {}).get('image_generation', 'volcengine')
        
        if not active_provider or active_provider not in self.config.get('ai_providers', {}):
            raise ValueError(f"图片生成服务提供商 '{active_provider}' 未配置或不存在")
        
        # 加载提供商配置
        provider_config = self.config['ai_providers'][active_provider]
        self.provider_name = active_provider
        self.api_key = provider_config.get('api_key')
        self.base_url = provider_config.get('base_url')
        self.timeout = provider_config.get('timeout', 60)
        
        # 加载模型配置
        model_config = provider_config.get('models', {}).get('image_generation')
        if not model_config:
            raise ValueError(f"提供商 '{active_provider}' 未配置图片生成模型")
        
        self.model = model_config.get('model_id')
        self.model_name = model_config.get('model_name')
        self.preset_sizes = model_config.get('preset_sizes', [])
        
        print(f"✓ 图片生成服务已初始化")
        print(f"  提供商: {self.provider_name}")
        print(f"  模型: {self.model_name}")
        print(f"  API地址: {self.base_url}")
    
    def _load_config(self, config_path):
        """加载配置文件"""
        config_file = Path(__file__).parent / config_path
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def generate(self, 
                prompt: str,
                width: int = 2048,
                height: int = 2048,
                seed: Optional[int] = None,
                reference_images: Optional[List[str]] = None,
                watermark: bool = False,
                guidance_scale: Optional[float] = None,
                **kwargs) -> Dict[str, Any]:
        """
        生成图片
        
        参数:
            prompt: 文字描述
            width: 图片宽度
            height: 图片高度
            seed: 随机种子(0-**********)
            reference_images: 参考图片URL列表(支持多图,最多4张)
            watermark: 是否添加水印
            guidance_scale: 引导系数(1.0-20.0)
            **kwargs: 其他参数
        
        返回:
            dict: 包含图片URL和元数据
        """
        start_time = time.time()
        
        try:
            # 参数验证
            is_valid, message = self.validate_prompt(prompt)
            if not is_valid:
                return {
                    'success': False,
                    'message': message,
                    'data': None
                }
            
            # 判断是否为图生图模式
            is_image_to_image = reference_images and len(reference_images) > 0
            
            # 构建尺寸参数
            if is_image_to_image:
                # 图生图模式: 使用 2K/4K 格式
                pixels = width * height
                image_size = '4K' if pixels >= 4194304 else '2K'
            else:
                # 文生图模式: 使用精确尺寸
                image_size = f"{width}x{height}"
            
            # 构建请求体
            request_body = {
                'model': self.model,
                'prompt': prompt,
                'size': image_size,
                'response_format': 'url',
                'watermark': watermark,
                'sequential_image_generation': 'disabled'  # 关闭组图功能
            }
            
            # 图生图模式: 添加参考图片
            if is_image_to_image:
                if len(reference_images) == 1:
                    # 单图模式: 字符串
                    request_body['image'] = reference_images[0]
                else:
                    # 多图模式: 数组(最多4张)
                    request_body['image'] = reference_images[:4]
            
            # 添加可选参数 - seed
            if seed is not None and seed != '' and seed != -1:
                seed_value = int(seed)
                if 0 <= seed_value <= **********:
                    request_body['seed'] = seed_value
            
            # 添加可选参数 - guidance_scale
            if guidance_scale is not None:
                scale_value = float(guidance_scale)
                if 1.0 <= scale_value <= 20.0:
                    request_body['guidance_scale'] = scale_value
            
            print("\n" + "="*60)
            print("调用火山引擎图片生成API")
            print("="*60)
            print(f"模式: {'图生图' if is_image_to_image else '文生图'}")
            if is_image_to_image:
                print(f"参考图片数量: {len(reference_images)}")
            print(f"提示词: {prompt}")
            print(f"尺寸: {width}x{height} ({image_size})")
            print(f"请求体: {json.dumps(request_body, ensure_ascii=False, indent=2)}")
            print("="*60 + "\n")
            
            # 调用火山引擎API
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {self.api_key}'
            }
            
            response = requests.post(
                f"{self.base_url}/images/generations",
                json=request_body,
                headers=headers,
                timeout=self.timeout
            )
            
            # 计算耗时
            duration_ms = int((time.time() - start_time) * 1000)
            
            # 检查HTTP状态码
            if response.status_code != 200:
                error_data = response.json() if response.text else {}
                error_message = error_data.get('error', {}).get('message', response.text)
                
                print(f"❌ API调用失败 (HTTP {response.status_code})")
                print(f"错误信息: {error_message}")
                
                return {
                    'success': False,
                    'message': f'API调用失败: {error_message}',
                    'data': {
                        'status_code': response.status_code,
                        'error': error_data,
                        'duration_ms': duration_ms
                    }
                }
            
            # 解析响应
            result = response.json()
            print("✅ API响应成功")
            print(f"响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            # 提取图片URL
            if result.get('data') and len(result['data']) > 0:
                image_url = result['data'][0].get('url')
                
                if not image_url:
                    return {
                        'success': False,
                        'message': 'API返回数据中未找到图片URL',
                        'data': result
                    }
                
                print("✅ 图片生成成功")
                print(f"图片URL: {image_url}")
                print(f"耗时: {duration_ms}ms")
                print("="*60 + "\n")
                
                return {
                    'success': True,
                    'message': '图片生成成功',
                    'data': {
                        'image_url': image_url,
                        'prompt': prompt,
                        'width': width,
                        'height': height,
                        'size': image_size,
                        'seed': request_body.get('seed'),
                        'model': result.get('model', self.model),
                        'created': result.get('created'),
                        'duration_ms': duration_ms,
                        'is_image_to_image': is_image_to_image,
                        'reference_count': len(reference_images) if is_image_to_image else 0
                    }
                }
            else:
                return {
                    'success': False,
                    'message': 'API返回数据格式异常',
                    'data': result
                }
                
        except requests.exceptions.Timeout:
            duration_ms = int((time.time() - start_time) * 1000)
            print(f"❌ 请求超时 ({duration_ms}ms)")
            
            return {
                'success': False,
                'message': f'请求超时,超过{self.timeout}秒',
                'data': {'duration_ms': duration_ms}
            }
            
        except requests.exceptions.RequestException as e:
            duration_ms = int((time.time() - start_time) * 1000)
            print(f"❌ 网络请求异常: {str(e)}")
            
            return {
                'success': False,
                'message': f'网络请求失败: {str(e)}',
                'data': {'duration_ms': duration_ms}
            }
            
        except Exception as e:
            duration_ms = int((time.time() - start_time) * 1000)
            print(f"❌ 未知错误: {str(e)}")
            
            return {
                'success': False,
                'message': f'生成失败: {str(e)}',
                'data': {'duration_ms': duration_ms}
            }
    
    def validate_prompt(self, prompt: str) -> tuple:
        """验证输入提示词"""
        if not prompt or len(prompt.strip()) == 0:
            return False, "提示词不能为空"
        
        if len(prompt) > 2000:
            return False, "提示词过长,请限制在2000字符以内"
        
        return True, "验证通过"
    
    def get_preset_sizes(self) -> List[Dict[str, Any]]:
        """获取预设尺寸列表"""
        return self.preset_sizes if self.preset_sizes else [
            {"name": "1:1", "width": 2048, "height": 2048},
            {"name": "4:3", "width": 2304, "height": 1728},
            {"name": "3:4", "width": 1728, "height": 2304},
            {"name": "16:9", "width": 2560, "height": 1440},
            {"name": "9:16", "width": 1440, "height": 2560}
        ]


# 测试代码
if __name__ == '__main__':
    generator = ImageGenerator()
    
    # 测试1: 文生图
    print("测试1: 文生图")
    result1 = generator.generate(
        prompt="一只可爱的橙色小猫坐在花园里",
        width=2048,
        height=2048,
        seed=12345
    )
    print(json.dumps(result1, ensure_ascii=False, indent=2))
    print("\n")
    
    # 测试2: 图生图(示例,需要真实的图片URL)
    # print("测试2: 图生图")
    # result2 = generator.generate(
    #     prompt="将这只猫的颜色改为白色",
    #     width=2048,
    #     height=2048,
    #     reference_images=["https://example.com/cat.jpg"]
    # )
    # print(json.dumps(result2, ensure_ascii=False, indent=2))
