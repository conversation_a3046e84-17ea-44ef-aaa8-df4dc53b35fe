"""
声音识别模块 - 讯飞实时转写+大模型API
负责实时语音转文字,支持说话人盲分(role_type=2)
"""
import json
import base64
import hmac
import hashlib
from datetime import datetime, timezone
from urllib.parse import urlencode, quote
from pathlib import Path
import asyncio
import websockets


class XFVoiceRecognizer:
    """讯飞语音识别器类 - 实时转写+大模型WebSocket流式识别"""
    
    STATUS_FIRST_FRAME = 0      # 第一帧
    STATUS_CONTINUE_FRAME = 1   # 中间帧
    STATUS_LAST_FRAME = 2       # 最后一帧
    
    def __init__(self, config_path='../config/config.json'):
        """初始化讯飞语音识别器"""
        config_path = Path(__file__).parent.parent / 'config' / 'config.json'
        
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = json.load(f)
        
        # 获取激活的提供商配置
        active_provider = self.config['active_providers']['voice_recognition']
        if not active_provider:
            raise ValueError("未配置语音识别提供商")
        
        provider_config = self.config['ai_providers'][active_provider]
        
        # 讯飞认证参数 (新API使用AccessKey)
        self.app_id = provider_config['app_id']
        self.access_key_id = provider_config['access_key_id']
        self.access_key_secret = provider_config['access_key_secret']
        self.base_url = provider_config['base_url']
        
        # 业务参数 (新API参数结构)
        model_config = provider_config['models']['voice_recognition']
        self.lang = model_config['lang']  # autodialect/autominor
        self.role_type = model_config['role_type']  # 2=盲分模式
        self.pd = model_config.get('pd', 'game')  # 垂直领域
        self.audio_encode = model_config['audio_encode']  # pcm_s16le
        self.samplerate = model_config['samplerate']  # 16000
        self.enable_speaker = model_config['enable_speaker_diarization']
        self.vad_eos = model_config.get('vad_eos', 1000)  # 静音断句时间(毫秒),默认1秒
        
        print(f"✅ 讯飞实时转写+大模型服务初始化成功")
        print(f"📍 APPID: {self.app_id}")
        print(f"🎤 说话人分离模式: role_type={self.role_type} (盲分)")
        print(f"⏱️ 断句静音时长: {self.vad_eos}ms")
        print(f"🌐 API地址: {self.base_url}")
    
    def create_url(self):
        """
        生成WebSocket鉴权URL (新API签名算法)
        返回: 带鉴权参数的wss URL
        """
        # 1. 生成UTC时间戳 (ISO 8601格式,带时区)
        utc_time = datetime.now(timezone.utc)
        utc_str = utc_time.strftime('%Y-%m-%dT%H:%M:%S') + utc_time.strftime('%z')
        # 格式化为 2025-09-04T15:38:07+0800
        if not utc_str.endswith('+0000'):
            utc_str = utc_str[:-2] + ':' + utc_str[-2:]  # +0800 -> +08:00
        utc_str = utc_str.replace('+00:00', '+0000')
        
        # 2. 构建请求参数字典 (除signature外的所有参数)
        params = {
            'appId': self.app_id,
            'accessKeyId': self.access_key_id,
            'utc': utc_str,
            'lang': self.lang,
            'audio_encode': self.audio_encode,
            'samplerate': str(self.samplerate),
            'role_type': str(self.role_type),  # 2=盲分模式
            'pd': self.pd,
            'vad_eos': str(self.vad_eos)  # 静音断句时长(毫秒)
        }
        
        # 3. 按参数名升序排列
        sorted_params = sorted(params.items())
        
        # 4. 对每个key、value进行URL编码后拼接
        encoded_pairs = []
        for key, value in sorted_params:
            encoded_key = quote(str(key), safe='')
            encoded_value = quote(str(value), safe='')
            encoded_pairs.append(f"{encoded_key}={encoded_value}")
        
        base_string = '&'.join(encoded_pairs)
        
        print(f"🔐 签名基础串: {base_string}")
        
        # 5. HMAC-SHA1加密 + Base64编码生成signature
        signature_bytes = hmac.new(
            self.access_key_secret.encode('utf-8'),
            base_string.encode('utf-8'),
            digestmod=hashlib.sha1
        ).digest()
        signature = base64.b64encode(signature_bytes).decode('utf-8')
        
        # 6. 将signature添加到参数中
        params['signature'] = signature
        
        # 7. 重新编码所有参数(包括signature)
        final_params = []
        for key in sorted(params.keys()):
            encoded_key = quote(str(key), safe='')
            encoded_value = quote(str(params[key]), safe='')
            final_params.append(f"{encoded_key}={encoded_value}")
        
        query_string = '&'.join(final_params)
        
        # 8. 拼接完整URL
        url = f"{self.base_url}?{query_string}"
        
        print(f"📡 WebSocket URL生成成功")
        print(f"🔑 Signature: {signature[:20]}...")
        
        return url
    
    async def recognize_stream(self, websocket_client, on_result_callback):
        """
        WebSocket流式识别处理 (新API协议)
        
        参数:
            websocket_client: 前端FastAPI WebSocket连接
            on_result_callback: 结果回调函数
        """
        url = self.create_url()
        
        try:
            async with websockets.connect(url) as ws:
                print("📡 已连接到讯飞实时转写+大模型服务")
                
                # 共享变量:会话ID
                session_id = None
                
                # 创建异步任务:接收前端音频 & 接收讯飞结果
                async def receive_from_client():
                    """接收前端音频数据并转发给讯飞"""
                    nonlocal session_id  # 使用外层的session_id
                    frame_count = 0
                    
                    while True:
                        try:
                            # FastAPI WebSocket接收消息
                            message = await websocket_client.receive_text()
                            data = json.loads(message)
                            
                            if data.get('type') == 'audio':
                                audio_b64 = data['audio']
                                
                                # ⭐ Base64解码为二进制数据
                                audio_bytes = base64.b64decode(audio_b64)
                                
                                # ⭐ 发送二进制帧(binary message)
                                await ws.send(audio_bytes)
                                
                                frame_count += 1
                                if frame_count % 50 == 0:
                                    print(f"📤 已发送 {frame_count} 帧音频 (每帧{len(audio_bytes)}字节)")
                            
                            elif data.get('type') == 'stop':
                                # ⭐ 发送结束信号JSON (需要sessionId)
                                if session_id:
                                    end_signal = {
                                        "end": True,
                                        "sessionId": session_id
                                    }
                                    await ws.send(json.dumps(end_signal))
                                    print(f"🛑 发送结束信号 (sessionId={session_id}, 共{frame_count}帧)")
                                else:
                                    print("⚠️ 未获取到sessionId,直接关闭连接")
                                break
                                
                        except json.JSONDecodeError:
                            print("❌ 音频数据格式错误")
                            continue
                        except Exception as e:
                            print(f"❌ 接收前端消息异常: {e}")
                            break
                
                async def receive_from_xfyun():
                    """接收讯飞识别结果并转发给前端"""
                    nonlocal session_id  # 共享sessionId给发送端
                    
                    # ⭐ 关键修复: 在整个会话期间维护说话人状态
                    # 讯飞的rl字段只在说话人切换时出现,需要跨seg_id保持状态
                    current_global_speaker = 0  # 全局说话人状态
                    
                    async for message in ws:
                        try:
                            result = json.loads(message)
                            
                            # 🔍 调试日志:查看原始返回数据
                            print(f"📥 讯飞返回: {json.dumps(result, ensure_ascii=False)}")
                            
                            msg_type = result.get('msg_type')
                            data = result.get('data', {})
                            
                            # ⭐ 保存sessionId(首次action消息会包含)
                            if 'sessionId' in data and not session_id:
                                session_id = data['sessionId']
                                print(f"🔑 获取到sessionId: {session_id}")
                            
                            if msg_type == 'action':
                                # 会话控制消息
                                action = data.get('action')
                                if action == 'started':
                                    print(f"✅ 会话已启动 (contextId={data.get('contextId')})")
                                elif action == 'end':
                                    code = data.get('code', '0')
                                    message_text = data.get('message', '')
                                    if code != '0':
                                        print(f"⚠️ 会话结束异常: {message_text} (code={code})")
                                    else:
                                        print(f"🏁 会话正常结束")
                                        
                            elif msg_type == 'error':
                                # 识别错误
                                error_msg = data.get('message', '未知错误')
                                await on_result_callback({
                                    'type': 'error',
                                    'message': error_msg
                                })
                                print(f"❌ 识别错误: {error_msg}")
                                
                            elif msg_type == 'result':
                                # 识别成功
                                res_type = result.get('res_type', '')
                                
                                if res_type == 'asr':
                                    # 语音识别结果
                                    cn_data = data.get('cn', {})
                                    st_data = cn_data.get('st', {})
                                    
                                    # 🔍 结果类型: type=0确定性结果(最终), type=1中间结果(实时)
                                    # ⚠️ 关键修复: type字段在st层级,不在rt层级!
                                    result_type = st_data.get('type', '1')
                                    is_final_type = (str(result_type) == '0')  # type=0为确定性结果
                                    
                                    rt_list = st_data.get('rt', [])
                                    
                                    # 🔍 ls=true表示该段语音的最后一帧(Last Segment)
                                    is_last_segment = data.get('ls', False)
                                    
                                    # 调试:输出type判断结果
                                    print(f"🔍 seg_id={data.get('seg_id')}: type={repr(result_type)}, is_final={is_final_type}, ls={is_last_segment}")
                                    
                                    # ⭐ 关键修复: 收集整个rt中的所有文本,并维护全局说话人状态
                                    # 讯飞规则: rl字段只在说话人切换时标记,后续词可能没有rl或标记为"0"
                                    # 策略: 只有当rl非"0"时才更新全局说话人,rl="0"表示延续之前的说话人
                                    all_text_parts = []
                                    
                                    for rt_item in rt_list:
                                        ws_list = rt_item.get('ws', [])
                                        
                                        for ws_item in ws_list:
                                            cw_list = ws_item.get('cw', [])
                                            
                                            for cw in cw_list:
                                                # 收集文本
                                                all_text_parts.append(cw.get('w', ''))
                                                
                                                # 更新全局说话人:只有rl非"0"时才更新
                                                # rl="0"表示延续之前的说话人,不是新说话人
                                                if 'rl' in cw:
                                                    rl_value = str(cw['rl'])
                                                    if rl_value != "0":  # 只有非"0"才是真正的说话人切换
                                                        try:
                                                            current_global_speaker = int(rl_value)
                                                            print(f"🔄 说话人切换: {current_global_speaker} (seg_id={data.get('seg_id')})")
                                                        except (ValueError, TypeError):
                                                            pass
                                    
                                    # 拼接完整文本
                                    full_text = ''.join(all_text_parts).strip()
                                    
                                    # 🔍 如果是最后一帧(ls=true),即使文本为空也要发送(用于触发前端清除)
                                    if full_text or is_last_segment:
                                        type_tag = "🔷最终" if is_final_type else "⚡实时"
                                        ls_tag = " [LS]" if is_last_segment else ""
                                        print(f"{type_tag}{ls_tag} 识别结果 - 文本: {full_text}, 说话人: {current_global_speaker}, seg_id: {data.get('seg_id')}")
                                        
                                        await on_result_callback({
                                            'type': 'result',
                                            'text': full_text,
                                            'speaker': current_global_speaker,  # 使用全局维护的说话人
                                            'is_final': is_final_type,  # 段落是否为最终修正版本
                                            'is_last': is_last_segment,  # 是否为整个语音的最后一帧
                                            'seg_id': data.get('seg_id')
                                        })
                        
                        except Exception as e:
                            print(f"❌ 处理讯飞结果异常: {e}")
                            import traceback
                            traceback.print_exc()
                
                # 并发执行收发任务
                await asyncio.gather(
                    receive_from_client(),
                    receive_from_xfyun()
                )
        
        except Exception as e:
            print(f"❌ WebSocket连接异常: {e}")
            import traceback
            traceback.print_exc()
            await on_result_callback({
                'type': 'error',
                'message': str(e)
            })


# 测试代码
if __name__ == '__main__':
    recognizer = XFVoiceRecognizer()
    print(f"✅ 初始化成功")
    print(f"📍 WebSocket URL: {recognizer.create_url()[:100]}...")
