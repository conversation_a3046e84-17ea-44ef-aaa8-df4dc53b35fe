"""
测试讯飞语音识别API鉴权
"""
import sys
sys.path.append('..')

from impl.voice_rec import XFVoiceRecognizer
import asyncio
import websockets
import json

async def test_auth():
    """测试鉴权和连接"""
    recognizer = XFVoiceRecognizer()
    
    print("=" * 60)
    print("讯飞语音识别鉴权测试")
    print("=" * 60)
    
    # 显示配置信息
    print(f"\n📋 配置信息:")
    print(f"  APPID: {recognizer.app_id}")
    print(f"  APIKey: {recognizer.api_key[:10]}...")
    print(f"  APISecret: {recognizer.api_secret[:10]}...")
    print(f"  Domain: {recognizer.domain}")
    print(f"  Language: {recognizer.language}")
    print(f"  Accent: {recognizer.accent}")
    print(f"  VAD EOS: {recognizer.vad_eos}")
    
    # 生成URL
    url = recognizer.create_url()
    print(f"\n🔗 WebSocket URL:")
    print(f"  {url[:150]}...")
    
    # 测试连接
    print(f"\n🔌 尝试连接讯飞服务器...")
    try:
        async with websockets.connect(url) as ws:
            print("✅ 连接成功!")
            
            # 发送第一帧测试数据
            test_frame = {
                "common": {
                    "app_id": recognizer.app_id
                },
                "business": {
                    "domain": recognizer.domain,
                    "language": recognizer.language,
                    "accent": recognizer.accent,
                    "vinfo": 1,
                    "vad_eos": recognizer.vad_eos
                },
                "data": {
                    "status": 0,
                    "format": "audio/L16;rate=16000",
                    "audio": "",  # 空音频测试
                    "encoding": "raw"
                }
            }
            
            print(f"\n📤 发送测试帧...")
            print(f"  Frame JSON: {json.dumps(test_frame, indent=2, ensure_ascii=False)[:300]}...")
            
            await ws.send(json.dumps(test_frame))
            print("✅ 发送成功!")
            
            # 接收响应
            print(f"\n📥 等待服务器响应...")
            response = await asyncio.wait_for(ws.recv(), timeout=5)
            result = json.loads(response)
            
            print(f"\n📨 服务器响应:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            code = result.get('code', -1)
            if code == 0:
                print("\n✅ 测试成功! 讯飞API鉴权通过!")
            else:
                print(f"\n❌ 测试失败! 错误码: {code}")
                print(f"   错误信息: {result.get('message', '未知错误')}")
                
    except asyncio.TimeoutError:
        print("❌ 超时: 服务器无响应")
    except Exception as e:
        print(f"❌ 连接失败: {e}")
    
    print("\n" + "=" * 60)

if __name__ == '__main__':
    asyncio.run(test_auth())
