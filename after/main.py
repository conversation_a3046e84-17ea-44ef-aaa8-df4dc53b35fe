"""
AI工具导航后端主程序
使用FastAPI框架提供RESTful API和WebSocket服务
"""
from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, List
import json
from pathlib import Path
from datetime import datetime
import hashlib
import time

# 导入各个功能模块
from impl.image_gen import ImageGenerator
from impl.video_gen import VideoGenerator
from impl.voice_rec import XFVoiceRecognizer
from impl.quality_check import QualityChecker
from impl.voiceprint import VoiceprintClient
from impl.voiceprint_db import VoiceprintDB



# 创建FastAPI应用
app = FastAPI(
    title="AI工具导航API",
    description="提供图片生成、视频生成、声音识别、项目质量检查等AI功能",
    version="1.0.0"
)

from fastapi.staticfiles import StaticFiles
from fastapi.responses import RedirectResponse

# 加载配置
config_path = Path(__file__).parent / 'config/config.json'
with open(config_path, 'r', encoding='utf-8') as f:
    config = json.load(f)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=config['server']['cors_origins'],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件 (项目根目录)
# 注意：main.py在after目录，项目根目录是父目录的父目录
project_root = Path(__file__).parent.parent
app.mount("/before", StaticFiles(directory=str(project_root / "before")), name="before")

# 初始化各个模块
image_generator = ImageGenerator()
video_generator = VideoGenerator()
voice_recognizer = XFVoiceRecognizer()
quality_checker = QualityChecker()
voiceprint_client = VoiceprintClient()
voiceprint_db = VoiceprintDB()

# 初始化声纹特征库
def init_voiceprint_group():
    """确保声纹特征库存在"""
    group_id = config['ai_providers']['voiceprint']['group_id']
    try:
        # 尝试创建特征库（如果已存在会返回相应错误，可以忽略）
        result = voiceprint_client.create_group(
            group_id=group_id,
            group_name="会议转写声纹库",
            group_info="用于会议转写的说话人声纹识别"
        )
        if result.get('success'):
            print(f"✅ 声纹特征库创建成功: {group_id}")
        else:
            # 检查是否是"已存在"的错误（code: 23001）
            error_msg = result.get('message', '')
            if 'exist' in error_msg.lower() or '23001' in error_msg:
                print(f"✅ 声纹特征库已存在: {group_id}")
            else:
                print(f"⚠️ 声纹特征库初始化警告: {error_msg}")
    except Exception as e:
        print(f"⚠️ 声纹特征库初始化异常: {e}")

# 启动时初始化
init_voiceprint_group()


# 请求模型定义
class ImageRequest(BaseModel):
    prompt: str
    width: int = 2048
    height: int = 2048
    seed: Optional[int] = None
    reference_images: Optional[List[str]] = None
    watermark: bool = False
    guidance_scale: Optional[float] = None

class VideoRequest(BaseModel):
    prompt: str
    duration: int = 5

class VoiceRequest(BaseModel):
    audio_data: str
    language: str = "zh"

class QualityRequest(BaseModel):
    code: str
    language: str = "python"

class VoiceprintRegisterRequest(BaseModel):
    user_name: str
    audio_data: str  # Base64编码的音频（MP3格式，3-5秒）

class VoiceprintThresholdRequest(BaseModel):
    threshold: float  # 匹配阈值 (0.0-1.0)

class VoiceprintMatchRequest(BaseModel):
    audio_data: str  # Base64编码的音频（MP3格式,1-5秒）


# API路由
@app.get("/")
async def root():
    """根路径重定向到语音识别页面"""
    return RedirectResponse(url="/before/html/voice-recognition.html")


@app.post("/api/image-generation")
async def generate_image(request: ImageRequest):
    """图片生成接口"""
    is_valid, message = image_generator.validate_prompt(request.prompt)
    if not is_valid:
        raise HTTPException(status_code=400, detail=message)
    
    # 准备参数
    kwargs = {
        'width': request.width,
        'height': request.height,
        'seed': request.seed,
        'watermark': request.watermark
    }
    
    # 添加参考图片(如果有)
    if request.reference_images and len(request.reference_images) > 0:
        kwargs['reference_images'] = request.reference_images
    
    # 添加引导系数(如果有)
    if request.guidance_scale is not None:
        kwargs['guidance_scale'] = request.guidance_scale
    
    # 调用生成器
    result = image_generator.generate(request.prompt, **kwargs)
    
    if not result['success']:
        raise HTTPException(status_code=500, detail=result['message'])
    
    return result


@app.get("/api/image-generation/preset-sizes")
async def get_preset_sizes():
    """获取预设尺寸列表"""
    return {
        'success': True,
        'data': image_generator.get_preset_sizes()
    }


@app.post("/api/video-generation")
async def generate_video(request: VideoRequest):
    """视频生成接口"""
    result = video_generator.generate(request.prompt, duration=request.duration)
    
    if not result['success']:
        raise HTTPException(status_code=400, detail=result['message'])
    
    return result


@app.get("/api/video-generation/status/{task_id}")
async def check_video_status(task_id: str):
    """查询视频生成任务状态"""
    result = video_generator.check_status(task_id)
    return result


@app.websocket("/ws/voice-recognition")
async def voice_recognition_ws(websocket: WebSocket):
    """
    WebSocket实时语音识别
    
    前端发送格式:
    {
        "type": "audio",
        "audio": "base64_encoded_pcm_data"
    }
    或
    {
        "type": "stop"
    }
    
    后端返回格式:
    {
        "type": "result",
        "text": "识别的文字",
        "speaker": 0,  // 说话人ID(盲分)
        "is_final": false
    }
    """
    await websocket.accept()
    print("📱 客户端已连接到语音识别WebSocket")
    
    try:
        # 定义结果回调函数
        async def send_result(result):
            """发送识别结果给前端"""
            await websocket.send_json(result)
        
        # 启动流式识别
        await voice_recognizer.recognize_stream(websocket, send_result)
        
    except WebSocketDisconnect:
        print("📱 客户端断开连接")
    except Exception as e:
        print(f"❌ WebSocket错误: {e}")
        try:
            await websocket.send_json({
                'type': 'error',
                'message': str(e)
            })
        except:
            pass
    finally:
        try:
            await websocket.close()
        except:
            pass




# ========================================
# 声纹管理API
# ========================================

@app.post("/api/voiceprint/register")
async def register_voiceprint(request: VoiceprintRegisterRequest):
    """
    注册声纹
    
    请求:
    {
        "user_name": "张三",
        "audio_data": "base64_encoded_mp3_audio"
    }
    """
    try:
        # 生成特征ID
        feature_id = hashlib.md5(f"{request.user_name}_{time.time()}".encode()).hexdigest()[:16]
        
        # 调用讯飞API创建特征
        feature_info = f"{request.user_name}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
        print(f"📝 注册声纹 - 用户: {request.user_name}, Feature ID: {feature_id}")
        print(f"📝 音频数据长度: {len(request.audio_data)} bytes")
        
        result = voiceprint_client.create_feature(
            group_id=config['ai_providers']['voiceprint']['group_id'],
            feature_id=feature_id,
            audio_base64=request.audio_data,
            feature_info=feature_info
        )
        
        print(f"📝 讯飞API响应: {result}")
        
        if not result['success']:
            print(f"❌ 讯飞API错误: {result.get('message', 'Unknown error')}")
            raise HTTPException(status_code=500, detail=result.get('message', '未知错误'))
        
        # 保存到数据库
        success = voiceprint_db.add_voiceprint(
            feature_id=feature_id,
            user_name=request.user_name,
            group_id=config['ai_providers']['voiceprint']['group_id'],
            feature_info=feature_info
        )
       
        if not success:
            raise HTTPException(status_code=500, detail='数据库保存失败')
        
        return {
            'success': True,
            'data': {
                'feature_id': feature_id,
                'user_name': request.user_name
            },
            'message': '声纹注册成功'
        }
    
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 声纹注册异常: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"注册失败: {str(e)}")


@app.get("/api/voiceprint/list")
async def get_voiceprint_list():
    """获取所有已注册的声纹列表"""
    try:
        voiceprints = voiceprint_db.get_all_voiceprints(
            group_id=config['ai_providers']['voiceprint']['group_id']
        )
        
        return {
            'success': True,
            'data': voiceprints
        }
    
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 声纹注册异常: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"注册失败: {str(e)}")


@app.delete("/api/voiceprint/delete/{feature_id}")
async def delete_voiceprint(feature_id: str):
    """删除指定声纹"""
    try:
        # 从讯飞删除
        xf_result = voiceprint_client.delete_feature(
            group_id=config['ai_providers']['voiceprint']['group_id'],
            feature_id=feature_id
        )
        
        # 从数据库删除
        db_success = voiceprint_db.delete_voiceprint(feature_id)
        
        if xf_result['success'] and db_success:
            return {
                'success': True,
                'message': '删除成功'
            }
        else:
            # 返回详细的错误信息
            error_msg = xf_result.get('message', '删除失败')
            raise HTTPException(status_code=500, detail=error_msg)
    
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 声纹删除异常: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")


@app.get("/api/voiceprint/threshold")
async def get_threshold():
    """获取当前匹配阈值"""
    return {
        'success': True,
        'data': {
            'threshold': config['ai_providers']['voiceprint']['match_threshold']
        }
    }


@app.post("/api/voiceprint/threshold")
async def update_threshold(request: VoiceprintThresholdRequest):
    """更新匹配阈值"""
    try:
        if not (0.0 <= request.threshold <= 1.0):
            raise HTTPException(status_code=400, detail='阈值必须在0.0-1.0之间')
        
        # 更新配置（仅内存，不持久化）
        config['ai_providers']['voiceprint']['match_threshold'] = request.threshold
        
        return {
            'success': True,
            'data': {
                'threshold': request.threshold
            },
            'message': '阈值更新成功'
        }
    
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@app.post("/api/voiceprint/match")
async def match_voiceprint(request: VoiceprintMatchRequest):
    """
    声纹匹配（1:N比对）
    
    请求:
    {
        "audio_data": "base64_encoded_mp3_audio"
    }
    
    响应:
    {
        "success": true,
        "data": {
            "matched": true,
            "user_name": "张三",
            "feature_id": "xxx",
            "score": 0.92
        }
    }
    """
    try:
        print(f"🔍 开始声纹匹配...")
        print(f"   音频数据长度: {len(request.audio_data)} bytes")
        
        # 调用讯飞1:N比对API
        result = voiceprint_client.search_feature_1n(
            group_id=config['ai_providers']['voiceprint']['group_id'],
            audio_base64=request.audio_data,
            top_k=5
        )
        
        print(f"   讯飞API响应: {result}")
        
        if not result['success']:
            return {
                'success': True,
                'data': {'matched': False},
                'message': '声纹比对失败'
            }
        
        # 获取匹配阈值
        threshold = config['ai_providers']['voiceprint']['match_threshold']
        
        # 解析比对结果
        score_list = result.get('data', {}).get('scoreList', [])
        
        if not score_list:
            print(f"❌ 未找到任何匹配的声纹")
            return {
                'success': True,
                'data': {'matched': False},
                'message': '未找到匹配的声纹'
            }
        
        # 取最高分的匹配结果
        best_match = score_list[0]
        score = best_match.get('score', 0)
        feature_id = best_match.get('featureId', '')
        
        print(f"   最佳匹配: feature_id={feature_id}, score={score}, threshold={threshold}")
        print(f"   所有匹配结果: {score_list}")
        
        # 判断是否达到阈值
        if score >= threshold:
            # 从数据库获取用户名
            user_name = voiceprint_db.get_user_name(feature_id)
            
            if user_name:
                print(f"✅ 声纹匹配成功: {user_name} (相似度: {score})")
                return {
                    'success': True,
                    'data': {
                        'matched': True,
                        'user_name': user_name,
                        'feature_id': feature_id,
                        'score': score
                    },
                    'message': '匹配成功'
                }
        
        print(f"❌ 声纹匹配失败: score={score} < threshold={threshold}")
        return {
            'success': True,
            'data': {
                'matched': False,
                'score': score,
                'feature_id': feature_id
            },
            'message': f'相似度({score})未达到阈值({threshold})'
        }
    
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 声纹匹配异常: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"匹配失败: {str(e)}")


@app.post("/api/quality-check")
async def check_quality(request: QualityRequest):
    """代码质量检查接口"""
    result = quality_checker.analyze(request.code, language=request.language)
    
    if not result['success']:
        raise HTTPException(status_code=400, detail=result['message'])
    
    return result


@app.get("/api/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy"}


# 启动说明
if __name__ == "__main__":
    import uvicorn
    
    print("=" * 50)
    print("AI工具导航后端服务")
    print("=" * 50)
    print(f"服务地址: http://localhost:{config['server']['port']}")
    print(f"API文档: http://localhost:{config['server']['port']}/docs")
    print("=" * 50)
    print("提示: 按 Ctrl+C 停止服务")
    print("=" * 50)
    
    # 使用字符串形式的导入路径以支持reload
    uvicorn.run(
        "main:app",
        host=config['server']['host'],
        port=config['server']['port'],
        reload=config['server']['debug']
    )
