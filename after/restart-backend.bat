@echo off
chcp 65001 >nul
title AI工具导航 - 后端服务

echo ========================================
echo 正在重启后端服务...
echo ========================================
echo.

REM 查找占用8000端口的进程
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8000 ^| findstr LISTENING') do (
    echo 发现占用端口的进程 PID: %%a
    taskkill /PID %%a /F >nul 2>&1
    echo 已停止旧服务
)

echo.
echo 等待1秒...
timeout /t 1 /nobreak >nul

echo.
echo 正在启动后端服务...
echo.
python main.py

pause
