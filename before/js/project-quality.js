(function () {
    // 配置
    const CONFIG = {
        gridSize: 20,
        nodeHeaderHeight: 40,
        portRadius: 6,
        wireCurvature: 0.5
    };

    // 状态
    const state = {
        nodes: [],
        links: [],
        transform: { x: 0, y: 0, scale: 1 },
        isDraggingNode: null,
        isPanning: false,
        isConnecting: null, // { nodeId, portIndex, type: 'input'|'output', x, y }
        lastMousePos: { x: 0, y: 0 },
        dragOffset: { x: 0, y: 0 },
        contextMenu: null // 当前显示的右键菜单
    };

    // DOM 元素
    const canvas = document.getElementById('connections-canvas');
    const ctx = canvas.getContext('2d');
    const nodesContainer = document.getElementById('nodes-container');
    const workspace = document.getElementById('workspace');

    // 预制节点定义
    const NODE_DEFINITIONS = {
        'CodeLoader': {
            title: '代码加载器 (Code Loader)',
            inputs: [],
            outputs: [{ name: '源码' }, { name: '文件路径' }],
            widgets: [{ type: 'text', label: '项目路径', value: 'D:/Project/Src' }, { type: 'button', label: '加载文件' }]
        },
        'QualityAnalyzer': {
            title: '质量分析器 (Quality Analyzer)',
            inputs: [{ name: '源码' }, { name: '配置' }],
            outputs: [{ name: '分析报告' }, { name: '分数' }],
            widgets: [{ type: 'select', label: '分析模式', options: ['深度扫描', '快速检查', '安全审计'] }]
        },
        'ReportGen': {
            title: '报告生成器 (Report Gen)',
            inputs: [{ name: '分析报告' }],
            outputs: [{ name: 'PDF' }, { name: 'HTML' }],
            widgets: [{ type: 'text', label: '输出文件名', value: 'report.pdf' }, { type: 'button', label: '导出' }]
        },
        'Note': {
            title: '备注 (Note)',
            inputs: [],
            outputs: [],
            widgets: [{ type: 'textarea', label: '内容', value: '在此输入备注...' }]
        }
    };

    // 初始化
    function init() {
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 绑定事件
        workspace.addEventListener('mousedown', handleMouseDown);
        window.addEventListener('mousemove', handleMouseMove);
        window.addEventListener('mouseup', handleMouseUp);
        workspace.addEventListener('wheel', handleWheel);
        workspace.addEventListener('contextmenu', handleContextMenu);

        // 全局点击关闭菜单
        window.addEventListener('click', () => hideContextMenu());

        // 创建示例节点
        createDemoNodes();

        // 开始渲染循环
        requestAnimationFrame(render);
    }

    function resizeCanvas() {
        canvas.width = workspace.clientWidth;
        canvas.height = workspace.clientHeight;
    }

    // 节点类
    class Node {
        constructor(id, title, x, y, inputs, outputs, widgets) {
            this.id = id;
            this.title = title;
            this.x = x;
            this.y = y;
            this.width = 220;
            this.inputs = inputs || [];
            this.outputs = outputs || [];
            this.widgets = widgets || [];
            this.element = null;

            this.createDOM();
        }

        createDOM() {
            const el = document.createElement('div');
            el.className = 'node';
            el.style.transform = `translate(${this.x}px, ${this.y}px)`;
            el.dataset.id = this.id;

            // 标题
            const header = document.createElement('div');
            header.className = 'node-header';
            header.innerHTML = `<span>${this.title}</span><div class="node-status-indicator"></div>`;
            el.appendChild(header);

            const body = document.createElement('div');
            body.className = 'node-body';

            // 输入端口
            this.inputs.forEach((input, index) => {
                const row = document.createElement('div');
                row.className = 'io-row';
                row.innerHTML = `
                    <div class="input-port" data-type="input" data-index="${index}"></div>
                    <span class="input-label">${input.name}</span>
                `;
                body.appendChild(row);
            });

            // 输出端口
            this.outputs.forEach((output, index) => {
                const row = document.createElement('div');
                row.className = 'io-row';
                row.innerHTML = `
                    <span class="output-label">${output.name}</span>
                    <div class="output-port" data-type="output" data-index="${index}"></div>
                `;
                body.appendChild(row);
            });

            // 控件
            this.widgets.forEach(widget => {
                const row = document.createElement('div');
                row.className = 'widget-row';

                if (widget.type === 'text') {
                    row.innerHTML = `
                        <label class="widget-label">${widget.label}</label>
                        <input type="text" value="${widget.value || ''}">
                    `;
                } else if (widget.type === 'number') {
                    row.innerHTML = `
                        <label class="widget-label">${widget.label}</label>
                        <input type="number" value="${widget.value || 0}">
                    `;
                } else if (widget.type === 'select') {
                    const options = widget.options.map(opt => `<option>${opt}</option>`).join('');
                    row.innerHTML = `
                        <label class="widget-label">${widget.label}</label>
                        <select>${options}</select>
                    `;
                } else if (widget.type === 'button') {
                    row.innerHTML = `<button>${widget.label}</button>`;
                } else if (widget.type === 'textarea') {
                    row.innerHTML = `
                        <label class="widget-label">${widget.label}</label>
                        <textarea rows="3">${widget.value || ''}</textarea>
                    `;
                }

                body.appendChild(row);
            });

            el.appendChild(body);
            nodesContainer.appendChild(el);
            this.element = el;

            // 绑定节点拖拽事件
            header.addEventListener('mousedown', (e) => {
                if (e.button !== 0) return; // 只响应左键
                e.stopPropagation(); // 阻止冒泡到 workspace
                state.isDraggingNode = this;
                state.dragOffset.x = e.clientX - this.x;
                state.dragOffset.y = e.clientY - this.y;
            });

            // 绑定端口事件
            const ports = el.querySelectorAll('.input-port, .output-port');
            ports.forEach(port => {
                port.addEventListener('mousedown', (e) => {
                    if (e.button !== 0) return; // 只响应左键
                    e.stopPropagation();
                    const type = port.dataset.type;
                    const index = parseInt(port.dataset.index);
                    const rect = port.getBoundingClientRect();
                    const centerX = rect.left + rect.width / 2;
                    const centerY = rect.top + rect.height / 2;

                    state.isConnecting = {
                        nodeId: this.id,
                        portIndex: index,
                        type: type,
                        startX: centerX,
                        startY: centerY,
                        currX: centerX,
                        currY: centerY
                    };
                });

                // 端口右键菜单 (删除连线)
                port.addEventListener('contextmenu', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    const type = port.dataset.type;
                    const index = parseInt(port.dataset.index);
                    showPortContextMenu(e.clientX, e.clientY, this.id, type, index);
                });
            });

            // 节点右键菜单 (删除节点)
            el.addEventListener('contextmenu', (e) => {
                e.preventDefault();
                e.stopPropagation();
                showNodeContextMenu(e.clientX, e.clientY, this);
            });
        }

        updatePosition(x, y) {
            this.x = x;
            this.y = y;
            this.element.style.transform = `translate(${x}px, ${y}px)`;
        }

        getPortPosition(type, index) {
            const portEl = this.element.querySelectorAll(`.${type}-port`)[index];
            if (!portEl) return { x: this.x, y: this.y };

            const rect = portEl.getBoundingClientRect();
            const canvasRect = canvas.getBoundingClientRect();

            return {
                x: rect.left - canvasRect.left + rect.width / 2,
                y: rect.top - canvasRect.top + rect.height / 2
            };
        }

        remove() {
            if (this.element) {
                this.element.remove();
            }
        }
    }

    // 交互处理
    function handleMouseDown(e) {
        if (e.button !== 0) return; // 只响应左键
        if (e.target === workspace || e.target === canvas) {
            state.isPanning = true;
            state.lastMousePos = { x: e.clientX, y: e.clientY };
        }
    }

    function handleMouseMove(e) {
        // 防止拖拽时选中文本
        if (state.isDraggingNode || state.isPanning || state.isConnecting) {
            e.preventDefault();
        }

        if (state.isDraggingNode) {
            const newX = e.clientX - state.dragOffset.x;
            const newY = e.clientY - state.dragOffset.y;
            state.isDraggingNode.updatePosition(newX, newY);
        } else if (state.isPanning) {
            const dx = e.clientX - state.lastMousePos.x;
            const dy = e.clientY - state.lastMousePos.y;

            state.nodes.forEach(node => {
                node.updatePosition(node.x + dx, node.y + dy);
            });

            state.lastMousePos = { x: e.clientX, y: e.clientY };
        } else if (state.isConnecting) {
            const rect = canvas.getBoundingClientRect();
            state.isConnecting.currX = e.clientX - rect.left;
            state.isConnecting.currY = e.clientY - rect.top;
        }
    }

    function handleMouseUp(e) {
        if (state.isConnecting) {
            // 检查是否释放到了另一个端口上
            const target = document.elementFromPoint(e.clientX, e.clientY);
            // 增加容错，检查 target 及其父元素是否是 port
            let portEl = target;
            if (!portEl.classList.contains('input-port') && !portEl.classList.contains('output-port')) {
                // 可能是点击到了伪元素区域或者内部
                if (portEl.parentElement && (portEl.parentElement.classList.contains('input-port') || portEl.parentElement.classList.contains('output-port'))) {
                    portEl = portEl.parentElement;
                } else {
                    portEl = null;
                }
            }

            if (portEl) {
                const targetType = portEl.dataset.type;
                if (targetType !== state.isConnecting.type) {
                    const nodeEl = portEl.closest('.node');
                    if (nodeEl) {
                        const targetNodeId = parseInt(nodeEl.dataset.id);
                        const targetPortIndex = parseInt(portEl.dataset.index);

                        // 检查是否已经存在连接
                        const exists = state.links.some(link =>
                            link.fromNode === (state.isConnecting.type === 'output' ? state.isConnecting.nodeId : targetNodeId) &&
                            link.fromPort === (state.isConnecting.type === 'output' ? state.isConnecting.portIndex : targetPortIndex) &&
                            link.toNode === (state.isConnecting.type === 'input' ? state.isConnecting.nodeId : targetNodeId) &&
                            link.toPort === (state.isConnecting.type === 'input' ? state.isConnecting.portIndex : targetPortIndex)
                        );

                        if (!exists) {
                            state.links.push({
                                fromNode: state.isConnecting.type === 'output' ? state.isConnecting.nodeId : targetNodeId,
                                fromPort: state.isConnecting.type === 'output' ? state.isConnecting.portIndex : targetPortIndex,
                                toNode: state.isConnecting.type === 'input' ? state.isConnecting.nodeId : targetNodeId,
                                toPort: state.isConnecting.type === 'input' ? state.isConnecting.portIndex : targetPortIndex
                            });
                        }
                    }
                }
            }
        }

        state.isDraggingNode = null;
        state.isPanning = false;
        state.isConnecting = null;
    }

    function handleWheel(e) {
        e.preventDefault();
        // 缩放逻辑暂略
    }

    // 右键菜单处理
    function handleContextMenu(e) {
        e.preventDefault();
        if (e.target === workspace || e.target === canvas) {
            showWorkspaceContextMenu(e.clientX, e.clientY);
        }
    }

    function showContextMenu(x, y, items) {
        hideContextMenu();

        const menu = document.createElement('div');
        menu.className = 'context-menu';
        menu.style.left = `${x}px`;
        menu.style.top = `${y}px`;

        items.forEach(item => {
            if (item.separator) {
                const sep = document.createElement('div');
                sep.className = 'context-menu-separator';
                menu.appendChild(sep);
            } else {
                const el = document.createElement('div');
                el.className = 'context-menu-item';
                el.innerHTML = `<span>${item.label}</span>`;
                el.addEventListener('click', (e) => {
                    e.stopPropagation();
                    item.action();
                    hideContextMenu();
                });
                menu.appendChild(el);
            }
        });

        document.body.appendChild(menu);
        state.contextMenu = menu;
    }

    function hideContextMenu() {
        if (state.contextMenu) {
            state.contextMenu.remove();
            state.contextMenu = null;
        }
    }

    function showWorkspaceContextMenu(x, y) {
        const items = [
            { label: '新建节点', action: () => { } }, // 占位标题
            { separator: true }
        ];

        // 添加预制节点选项
        Object.keys(NODE_DEFINITIONS).forEach(key => {
            const def = NODE_DEFINITIONS[key];
            items.push({
                label: `添加 ${def.title}`,
                action: () => {
                    addNode(key, x, y);
                }
            });
        });

        showContextMenu(x, y, items);
    }

    function showNodeContextMenu(x, y, node) {
        const items = [
            { label: node.title, action: () => { } },
            { separator: true },
            { label: '删除节点', action: () => deleteNode(node.id) }
        ];
        showContextMenu(x, y, items);
    }

    function showPortContextMenu(x, y, nodeId, type, index) {
        // 查找连接到此端口的连线
        const relatedLinks = state.links.filter(link => {
            if (type === 'input') {
                return link.toNode === nodeId && link.toPort === index;
            } else {
                return link.fromNode === nodeId && link.fromPort === index;
            }
        });

        if (relatedLinks.length === 0) return;

        const items = [
            {
                label: '断开连接', action: () => {
                    state.links = state.links.filter(link => !relatedLinks.includes(link));
                }
            }
        ];
        showContextMenu(x, y, items);
    }

    // 逻辑操作
    function addNode(type, x, y) {
        // 转换坐标 (简单处理，假设没有缩放和平移)
        // 实际应该减去 workspace 的 offset
        const def = NODE_DEFINITIONS[type];
        if (def) {
            const id = Date.now(); // 简单 ID
            const node = new Node(id, def.title, x, y, def.inputs, def.outputs, def.widgets);
            state.nodes.push(node);
        }
    }

    function deleteNode(nodeId) {
        const nodeIndex = state.nodes.findIndex(n => n.id === nodeId);
        if (nodeIndex !== -1) {
            const node = state.nodes[nodeIndex];
            node.remove();
            state.nodes.splice(nodeIndex, 1);

            // 删除相关连线
            state.links = state.links.filter(link => link.fromNode !== nodeId && link.toNode !== nodeId);
        }
    }

    // 渲染
    function render() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // 绘制已存在的连线
        ctx.strokeStyle = '#a0a0a0';
        ctx.lineWidth = 2;

        state.links.forEach(link => {
            const fromNode = state.nodes.find(n => n.id === link.fromNode);
            const toNode = state.nodes.find(n => n.id === link.toNode);

            if (fromNode && toNode) {
                const start = fromNode.getPortPosition('output', link.fromPort);
                const end = toNode.getPortPosition('input', link.toPort);
                drawCurve(ctx, start.x, start.y, end.x, end.y);
            }
        });

        // 绘制正在拖拽的连线
        if (state.isConnecting) {
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 2;

            // 获取起始端口的实时位置
            const node = state.nodes.find(n => n.id === state.isConnecting.nodeId);
            if (node) {
                const start = node.getPortPosition(state.isConnecting.type, state.isConnecting.portIndex);
                drawCurve(ctx, start.x, start.y, state.isConnecting.currX, state.isConnecting.currY);
            }
        }

        requestAnimationFrame(render);
    }

    function drawCurve(ctx, x1, y1, x2, y2) {
        ctx.beginPath();
        ctx.moveTo(x1, y1);

        // 贝塞尔曲线控制点
        const dist = Math.abs(x2 - x1) * 0.5;
        const cp1x = x1 + dist;
        const cp1y = y1;
        const cp2x = x2 - dist;
        const cp2y = y2;

        ctx.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, x2, y2);
        ctx.stroke();
    }

    // 创建示例数据
    function createDemoNodes() {
        addNode('CodeLoader', 100, 100);
        addNode('QualityAnalyzer', 500, 150);
        addNode('ReportGen', 900, 200);

        // 这里的 ID 需要匹配上面生成的 ID，但因为 addNode 使用 Date.now()，这里手动创建比较麻烦
        // 为了演示，我们清空 nodes 并手动创建带固定 ID 的
        state.nodes.forEach(n => n.remove());
        state.nodes = [];

        const node1 = new Node(1, '代码加载器 (Code Loader)', 100, 100,
            [],
            [{ name: '源码' }, { name: '文件路径' }],
            [{ type: 'text', label: '项目路径', value: 'D:/Project/Src' }, { type: 'button', label: '加载文件' }]
        );

        const node2 = new Node(2, '质量分析器 (Quality Analyzer)', 500, 150,
            [{ name: '源码' }, { name: '配置' }],
            [{ name: '分析报告' }, { name: '分数' }],
            [{ type: 'select', label: '分析模式', options: ['深度扫描', '快速检查', '安全审计'] }]
        );

        const node3 = new Node(3, '报告生成器 (Report Gen)', 900, 200,
            [{ name: '分析报告' }],
            [{ name: 'PDF' }, { name: 'HTML' }],
            [{ type: 'text', label: '输出文件名', value: 'report.pdf' }, { type: 'button', label: '导出' }]
        );

        state.nodes.push(node1, node2, node3);
        state.links.push({ fromNode: 1, fromPort: 0, toNode: 2, toPort: 0 });
    }

    // 启动
    init();

})();
