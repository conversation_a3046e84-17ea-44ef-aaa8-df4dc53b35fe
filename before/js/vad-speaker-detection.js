/**
 * VAD静音检测 - 说话人自动切换
 * 通过检测静音时长来模拟说话人切换
 */

class VADSpeakerDetector {
    constructor(options = {}) {
        // 配置参数
        this.silenceThreshold = options.silenceThreshold || 0.01;  // 静音阈值
        this.silenceDuration = options.silenceDuration || 2000;    // 静音时长(ms),超过则切换说话人
        this.maxSpeakers = options.maxSpeakers || 4;               // 最多说话人数
        
        // 状态变量
        this.currentSpeaker = 0;
        this.lastSoundTime = Date.now();
        this.isSilent = false;
        this.silenceTimer = null;
    }
    
    /**
     * 检测音频音量
     * @param {Float32Array} audioData - 音频数据
     * @returns {number} 音量值 (0-1)
     */
    getVolume(audioData) {
        let sum = 0;
        for (let i = 0; i < audioData.length; i++) {
            sum += audioData[i] * audioData[i];
        }
        return Math.sqrt(sum / audioData.length);
    }
    
    /**
     * 更新音频数据并检测是否需要切换说话人
     * @param {Float32Array} audioData - 音频数据
     * @returns {number|null} 如果需要切换,返回新的说话人ID,否则返回null
     */
    update(audioData) {
        const volume = this.getVolume(audioData);
        const now = Date.now();
        
        if (volume > this.silenceThreshold) {
            // 检测到声音
            this.lastSoundTime = now;
            
            if (this.isSilent) {
                // 从静音状态恢复,切换到下一个说话人
                this.isSilent = false;
                this.currentSpeaker = (this.currentSpeaker + 1) % this.maxSpeakers;
                console.log(`🔄 检测到新的说话声音,切换到说话人 ${this.currentSpeaker + 1}`);
                return this.currentSpeaker;
            }
        } else {
            // 检测到静音
            const silenceDuration = now - this.lastSoundTime;
            
            if (silenceDuration > this.silenceDuration && !this.isSilent) {
                this.isSilent = true;
                console.log(`🔇 检测到长时间静音 (${(silenceDuration/1000).toFixed(1)}秒)`);
            }
        }
        
        return null;  // 不需要切换
    }
    
    /**
     * 获取当前说话人ID
     */
    getCurrentSpeaker() {
        return this.currentSpeaker;
    }
    
    /**
     * 重置检测器
     */
    reset() {
        this.currentSpeaker = 0;
        this.lastSoundTime = Date.now();
        this.isSilent = false;
        clearTimeout(this.silenceTimer);
    }
    
    /**
     * 手动切换到下一个说话人
     */
    switchToNextSpeaker() {
        this.currentSpeaker = (this.currentSpeaker + 1) % this.maxSpeakers;
        this.lastSoundTime = Date.now();
        this.isSilent = false;
        console.log(`👆 手动切换到说话人 ${this.currentSpeaker + 1}`);
        return this.currentSpeaker;
    }
    
    /**
     * 手动设置说话人
     */
    setSpeaker(speakerId) {
        if (speakerId >= 0 && speakerId < this.maxSpeakers) {
            this.currentSpeaker = speakerId;
            this.lastSoundTime = Date.now();
            this.isSilent = false;
            console.log(`👆 手动设置为说话人 ${speakerId + 1}`);
            return speakerId;
        }
        return null;
    }
}

// 使用示例
/*
const vadDetector = new VADSpeakerDetector({
    silenceThreshold: 0.01,   // 音量低于此值视为静音
    silenceDuration: 2000,    // 静音超过2秒切换说话人
    maxSpeakers: 4            // 最多4个说话人轮换
});

// 在音频处理回调中
processor.onaudioprocess = (e) => {
    const audioData = e.inputBuffer.getChannelData(0);
    
    // 检测是否需要切换说话人
    const newSpeaker = vadDetector.update(audioData);
    if (newSpeaker !== null) {
        // 更新当前说话人
        currentSpeaker = newSpeaker;
    }
    
    // 发送识别请求时附带说话人ID
    ws.send(JSON.stringify({
        type: 'audio',
        audio: base64Audio,
        speaker: vadDetector.getCurrentSpeaker()
    }));
};
*/
