/**
 * 智能语音转写 - 专业版逻辑
 * 核心功能：实时录音、WebSocket流式传输、横向声纹波形、文档流渲染
 */

class VoiceTranscriber {
    constructor() {
        // 状态变量
        this.isRecording = false;
        this.startTime = 0;
        this.timerInterval = null;
        this.results = [];

        // 音频相关
        this.audioContext = null;
        this.mediaStream = null;
        this.analyser = null;
        this.processor = null;
        this.ws = null;

        // 声纹匹配相关
        this.speakerAudioCache = {};  // {speaker_id: [audio_chunks]}
        this.speakerNameMap = {};     // {speaker_id: "真实姓名"}
        this.currentSpeaker = -1;
        this.enableVoiceprint = false;
        this.audioChunksBuffer = [];  // 缓存当前说话人的音频数据
        this.lastSpeaker = -1;

        // DOM 元素
        this.dom = {
            startBtn: document.getElementById('startBtn'),
            stopBtn: document.getElementById('stopBtn'),
            clearBtn: document.getElementById('clearBtn'),
            copyAllBtn: document.getElementById('copyAllBtn'),
            exportBtn: document.getElementById('exportBtn'),
            tipsToggle: document.getElementById('tipsToggle'),
            closeHelpBtn: document.getElementById('closeHelpBtn'),
            helpModal: document.getElementById('helpModal'),
            resultsList: document.getElementById('resultsList'),
            statusBadge: document.getElementById('statusBadge'),
            statusDot: document.querySelector('.status-dot'),
            statusText: document.querySelector('.status-text'),
            timer: document.getElementById('recordingTimer'),
            waveformCanvas: document.getElementById('waveformCanvas'),
            documentArea: document.getElementById('documentArea'),
            enableVoiceprint: document.getElementById('enableVoiceprint')
        };

        // 初始化
        this.init();
    }

    init() {
        this.bindEvents();
        this.setupShortcuts();
        this.initWaveform();
        console.log('🚀 智能语音转写系统已就绪');
    }

    bindEvents() {
        this.dom.startBtn.addEventListener('click', () => this.startRecording());
        this.dom.stopBtn.addEventListener('click', () => this.stopRecording());
        this.dom.clearBtn.addEventListener('click', () => this.clearResults());
        this.dom.copyAllBtn.addEventListener('click', () => this.copyAll());
        this.dom.exportBtn.addEventListener('click', () => this.exportText());

        // 帮助模态框
        this.dom.tipsToggle.addEventListener('click', () => {
            this.dom.helpModal.classList.add('active');
        });
        this.dom.closeHelpBtn.addEventListener('click', () => {
            this.dom.helpModal.classList.remove('active');
        });
        this.dom.helpModal.addEventListener('click', (e) => {
            if (e.target === this.dom.helpModal) {
                this.dom.helpModal.classList.remove('active');
            }
        });
    }

    setupShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space' && !e.target.matches('input, textarea')) {
                e.preventDefault();
                if (this.isRecording) {
                    this.stopRecording();
                } else {
                    this.startRecording();
                }
            }
        });
    }

    // ==========================================
    // 录音核心逻辑
    // ==========================================

    async startRecording() {
        if (this.isRecording) return;

        try {
            // 检查是否启用声纹匹配
            this.enableVoiceprint = this.dom.enableVoiceprint.checked;
            
            // 重置声纹相关状态
            this.speakerNameMap = {};
            this.currentSpeaker = -1;
            
            if (this.enableVoiceprint) {
                console.log('✅ 已启用声纹匹配模式');
            }

            // 1. 获取麦克风权限
            this.updateStatus('connecting', '正在连接...');
            this.mediaStream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    channelCount: 1,
                    sampleRate: 16000,
                    echoCancellation: true,
                    noiseSuppression: true
                }
            });

            // 2. 连接 WebSocket
            await this.connectWebSocket();

            // 3. 初始化音频处理
            this.initAudioProcessing();

            // 4. 更新状态
            this.isRecording = true;
            this.updateUIState(true);
            this.startTimer();
            this.updateStatus('recording', '正在录音');
            this.drawWaveform();

            // 移除空状态
            const emptyState = this.dom.resultsList.querySelector('.empty-state');
            if (emptyState) emptyState.remove();

        } catch (error) {
            console.error('启动失败:', error);
            this.updateStatus('error', '启动失败');
            alert('无法启动录音: ' + error.message);
        }
    }

    stopRecording() {
        if (!this.isRecording) return;

        this.isRecording = false;
        this.stopTimer();
        this.updateUIState(false);
        this.updateStatus('ready', '准备就绪');

        // 停止音频流
        if (this.mediaStream) {
            this.mediaStream.getTracks().forEach(track => track.stop());
        }

        // 关闭 WebSocket
        if (this.ws) {
            this.ws.send(JSON.stringify({ type: 'stop' }));
            this.ws.close();
        }

        // 关闭音频上下文
        if (this.audioContext) {
            this.audioContext.close();
        }
    }

    connectWebSocket() {
        return new Promise((resolve, reject) => {
            this.ws = new WebSocket('ws://127.0.0.1:8000/ws/voice-recognition');

            this.ws.onopen = () => resolve();
            this.ws.onerror = (err) => reject(err);

            this.ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                if (data.type === 'result') {
                    this.handleResult(data);
                }
            };
        });
    }

    initAudioProcessing() {
        const AudioContext = window.AudioContext || window.webkitAudioContext;
        this.audioContext = new AudioContext({ sampleRate: 16000 });
        const source = this.audioContext.createMediaStreamSource(this.mediaStream);

        // 分析器 (用于波形图)
        this.analyser = this.audioContext.createAnalyser();
        this.analyser.fftSize = 2048;
        source.connect(this.analyser);

        // 处理器 (用于发送数据)
        this.processor = this.audioContext.createScriptProcessor(1024, 1, 1);
        this.processor.onaudioprocess = (e) => {
            if (!this.isRecording) return;

            const inputData = e.inputBuffer.getChannelData(0);
            // 转换为PCM并发送
            const pcmData = this.floatTo16BitPCM(inputData);
            const audioBase64 = this.arrayBufferToBase64(pcmData.buffer);
            
            this.ws.send(JSON.stringify({
                type: 'audio',
                audio: audioBase64
            }));
            
            // 如果启用声纹匹配,缓存音频数据用于后续比对
            if (this.enableVoiceprint) {
                // 复制一份避免引用问题
                const audioCopy = new Float32Array(inputData);
                this.audioChunksBuffer.push(audioCopy);
            }
        };

        source.connect(this.processor);
        this.processor.connect(this.audioContext.destination);
    }

    async handleResult(data) {
        console.log('收到识别结果:', data);

        // 仅处理最终结果，忽略中间状态以保持界面整洁
        if (!data.is_final) return;
        if (!data.text || !data.text.trim()) return;

        // 修复：去除文本开头的标点符号
        let cleanText = data.text.trim().replace(/^[，。！？、,.!?]+/, '');
        if (!cleanText) return;

        // 检测说话人切换,进行声纹匹配
        if (this.enableVoiceprint) {
            // ⭐ 方案1: 在收到最终修正结果(is_final=true)时触发声纹匹配
            // ⭐ 方案2: 或者在录音真正结束(is_last=true)时触发
            // 由于讯飞可能在前端关闭连接前没发送ls=true,所以优先使用方案1
            if (data.is_final || data.is_last) {
                console.log(`🏁 ${data.is_final ? '收到最终结果' : '录音结束'},开始声纹匹配 (缓存音频块数: ${this.audioChunksBuffer.length})`);
                const matchedName = await this.matchVoiceprint(data.speaker);
                // ⭐ 关键修复: 如果匹配成功,直接保存真实姓名到result对象
                if (matchedName) {
                    data.matched_name = matchedName;
                }
            }
        }

        const result = {
            text: cleanText,
            speaker: data.speaker,
            matched_name: data.matched_name || null,  // ⭐ 保存匹配到的真实姓名
            time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' })
        };

        this.results.push(result);
        this.renderResult(result);
        
        // ⭐ 自动滚动到底部
        this.scrollToBottom();
    }

    async matchVoiceprint(speakerId) {
        // 如果没有足够的音频数据(少于1秒),跳过
        if (this.audioChunksBuffer.length < 16) { // 16000Hz / 1024 ≈ 15.6 chunks/sec
            console.log(`⚠️ 音频数据不足(${this.audioChunksBuffer.length}块),跳过声纹匹配`);
            this.audioChunksBuffer = [];
            return null;  // ⭐ 返回null表示未匹配
        }

        try {
            console.log(`🎤 开始声纹匹配 (音频块数: ${this.audioChunksBuffer.length})...`);
            
            // 将音频块合并并转换为MP3
            const mp3Base64 = await this.convertAudioToMP3(this.audioChunksBuffer);
            console.log(`📦 MP3转换完成,大小: ${mp3Base64.length} bytes`);
            
            // 调用后端声纹匹配API
            const response = await fetch('http://127.0.0.1:8000/api/voiceprint/match', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ audio_data: mp3Base64 })
            });
            
            const result = await response.json();
            console.log('🔍 声纹匹配API响应:', JSON.stringify(result, null, 2));
            
            if (result.success && result.data.matched) {
                const userName = result.data.user_name;
                console.log(`✅ 声纹匹配成功!`);
                console.log(`   👤 用户名: ${userName}`);
                console.log(`   📊 相似度: ${result.data.score}`);
                console.log(`   🆔 特征ID: ${result.data.feature_id}`);
                
                // ⭐ 返回匹配到的用户名,不再更新speakerNameMap
                return userName;
            } else {
                console.log(`❌ 声纹匹配失败`);
                if (result.data && result.data.score !== undefined) {
                    console.log(`   📊 最高相似度: ${result.data.score}`);
                    console.log(`   ⚠️ 未达到阈值,无法识别说话人`);
                }
                return null;
            }
        } catch (error) {
            console.error('❌ 声纹匹配出错:', error);
            return null;
        } finally {
            // 清空缓存,准备下次匹配
            this.audioChunksBuffer = [];
        }
    }

    async convertAudioToMP3(audioChunks) {
        // 合并所有音频块
        const totalLength = audioChunks.reduce((sum, chunk) => sum + chunk.length, 0);
        const mergedAudio = new Float32Array(totalLength);
        let offset = 0;
        for (const chunk of audioChunks) {
            mergedAudio.set(chunk, offset);
            offset += chunk.length;
        }

        // 转换为16位PCM
        const pcmData = new Int16Array(mergedAudio.length);
        for (let i = 0; i < mergedAudio.length; i++) {
            const s = Math.max(-1, Math.min(1, mergedAudio[i]));
            pcmData[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
        }

        // 使用lamejs编码为MP3
        const mp3encoder = new lamejs.Mp3Encoder(1, 16000, 128);
        const mp3Data = [];
        
        const blockSize = 1152;
        for (let i = 0; i < pcmData.length; i += blockSize) {
            const chunk = pcmData.subarray(i, Math.min(i + blockSize, pcmData.length));
            const mp3buf = mp3encoder.encodeBuffer(chunk);
            if (mp3buf.length > 0) {
                mp3Data.push(mp3buf);
            }
        }
        
        const mp3buf = mp3encoder.flush();
        if (mp3buf.length > 0) {
            mp3Data.push(mp3buf);
        }

        // 合并所有MP3数据并转为Base64
        const mp3Blob = new Blob(mp3Data, { type: 'audio/mp3' });
        return new Promise((resolve) => {
            const reader = new FileReader();
            reader.onloadend = () => resolve(reader.result.split(',')[1]);
            reader.readAsDataURL(mp3Blob);
        });
    }

    scrollToBottom() {
        // ⭐ 自动滚动到最新消息
        const resultsList = this.dom.resultsList;
        resultsList.scrollTop = resultsList.scrollHeight;
    }

    renderResult(result) {
        const div = document.createElement('div');
        div.className = 'transcript-line';

        // 修复：说话人编号直接使用后端返回的值
        const speakerId = result.speaker !== null ? result.speaker : '?';

        // 颜色索引：如果后端返回1-based，需要减1来映射到0-3的颜色
        const colorIndex = (typeof result.speaker === 'number' && result.speaker > 0)
            ? result.speaker - 1
            : (result.speaker || 0);

        const speakerClass = `speaker-${colorIndex % 4}`;
        
        // ⭐ 关键修复: 优先使用result中保存的matched_name,而不是从speakerNameMap查找
        const speakerName = result.matched_name || `说话人 ${speakerId}`;

        div.innerHTML = `
            <div class="line-meta">
                <span class="speaker-name ${speakerClass}">${speakerName}</span>
                <span class="timestamp">${result.time}</span>
            </div>
            <div class="line-content final">
                ${result.text}
            </div>
        `;

        this.dom.resultsList.appendChild(div);

        // 自动滚动到底部
        this.dom.documentArea.scrollTop = this.dom.documentArea.scrollHeight;
    }

    // ==========================================
    // 视觉效果 (横向声纹波形)
    // ==========================================

    initWaveform() {
        const canvas = this.dom.waveformCanvas;
        canvas.width = canvas.offsetWidth;
        canvas.height = canvas.offsetHeight;

        window.addEventListener('resize', () => {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        });
    }

    drawWaveform() {
        if (!this.isRecording) return;

        const canvas = this.dom.waveformCanvas;
        const ctx = canvas.getContext('2d');
        const width = canvas.width;
        const height = canvas.height;

        const bufferLength = this.analyser.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);
        this.analyser.getByteTimeDomainData(dataArray);

        ctx.clearRect(0, 0, width, height);
        ctx.lineWidth = 2;
        ctx.strokeStyle = '#3b82f6'; // 科技蓝
        ctx.beginPath();

        const sliceWidth = width * 1.0 / bufferLength;
        let x = 0;

        for (let i = 0; i < bufferLength; i++) {
            const v = dataArray[i] / 128.0;
            const y = v * height / 2;

            if (i === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }

            x += sliceWidth;
        }

        ctx.lineTo(canvas.width, canvas.height / 2);
        ctx.stroke();

        requestAnimationFrame(() => this.drawWaveform());
    }

    // ==========================================
    // 辅助功能
    // ==========================================

    startTimer() {
        this.startTime = Date.now();
        this.timerInterval = setInterval(() => {
            const diff = Date.now() - this.startTime;
            const date = new Date(diff);
            const str = date.toISOString().substr(11, 8);
            this.dom.timer.textContent = str;
        }, 1000);
    }

    stopTimer() {
        clearInterval(this.timerInterval);
    }

    updateUIState(isRecording) {
        this.dom.startBtn.disabled = isRecording;
        this.dom.stopBtn.disabled = !isRecording;

        if (isRecording) {
            this.dom.startBtn.style.display = 'none';
            this.dom.stopBtn.style.display = 'flex';
        } else {
            this.dom.startBtn.style.display = 'flex';
            this.dom.stopBtn.style.display = 'none';
        }
    }

    updateStatus(type, text) {
        this.dom.statusText.textContent = text;
        this.dom.statusDot.className = 'status-dot';
        if (type === 'recording') this.dom.statusDot.classList.add('recording');
        if (type === 'ready') this.dom.statusDot.classList.add('active');
    }

    clearResults() {
        if (confirm('确定要清空所有内容吗？')) {
            this.results = [];
            this.dom.resultsList.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">🎙️</div>
                    <h2>开始您的语音转写</h2>
                    <p>点击下方麦克风按钮，实时将语音转换为文字</p>
                    <div class="features-grid">
                        <div class="feature-item">
                            <span class="icon">👥</span>
                            <span>多人分离</span>
                        </div>
                        <div class="feature-item">
                            <span class="icon">⚡</span>
                            <span>实时上屏</span>
                        </div>
                        <div class="feature-item">
                            <span class="icon">📝</span>
                            <span>自动排版</span>
                        </div>
                    </div>
                </div>
            `;
            this.dom.timer.textContent = '00:00:00';
        }
    }

    copyAll() {
        const text = this.results.map(r =>
            `[${r.time}] 说话人${r.speaker !== null ? r.speaker : '?'}: ${r.text}`
        ).join('\n');

        navigator.clipboard.writeText(text).then(() => {
            alert('已复制全部内容');
        });
    }

    exportText() {
        const text = this.results.map(r =>
            `[${r.time}] 说话人${r.speaker !== null ? r.speaker : '?'}: ${r.text}`
        ).join('\n');

        const blob = new Blob([text], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `会议转写_${new Date().toISOString().slice(0, 10)}.txt`;
        a.click();
    }

    // 工具函数
    floatTo16BitPCM(input) {
        const output = new Int16Array(input.length);
        for (let i = 0; i < input.length; i++) {
            const s = Math.max(-1, Math.min(1, input[i]));
            output[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
        }
        return output;
    }

    arrayBufferToBase64(buffer) {
        let binary = '';
        const bytes = new Uint8Array(buffer);
        for (let i = 0; i < bytes.byteLength; i++) {
            binary += String.fromCharCode(bytes[i]);
        }
        return btoa(binary);
    }
}

// 启动应用
document.addEventListener('DOMContentLoaded', () => {
    window.voiceTranscriber = new VoiceTranscriber();
    window.voiceprintManager = new VoiceprintManager();

    console.log('🎙️ 语音转写系统已就绪');
    console.log('🔊 声纹管理模块已加载');
});
