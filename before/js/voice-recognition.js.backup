/**
 * 智能语音转写 - 专业版逻辑
 * 核心功能：实时录音、WebSocket流式传输、横向声纹波形、文档流渲染
 */

class VoiceTranscriber {
    constructor() {
        // 状态变量
        this.isRecording = false;
        this.startTime = 0;
        this.timerInterval = null;
        this.results = [];

        // 音频相关
        this.audioContext = null;
        this.mediaStream = null;
        this.analyser = null;
        this.processor = null;
        this.ws = null;

        // DOM 元素
        this.dom = {
            startBtn: document.getElementById('startBtn'),
            stopBtn: document.getElementById('stopBtn'),
            clearBtn: document.getElementById('clearBtn'),
            copyAllBtn: document.getElementById('copyAllBtn'),
            exportBtn: document.getElementById('exportBtn'),
            tipsToggle: document.getElementById('tipsToggle'),
            closeHelpBtn: document.getElementById('closeHelpBtn'),
            helpModal: document.getElementById('helpModal'),
            resultsList: document.getElementById('resultsList'),
            statusBadge: document.getElementById('statusBadge'),
            statusDot: document.querySelector('.status-dot'),
            statusText: document.querySelector('.status-text'),
            timer: document.getElementById('recordingTimer'),
            waveformCanvas: document.getElementById('waveformCanvas'),
            documentArea: document.getElementById('documentArea')
        };

        // 初始化
        this.init();
    }

    init() {
        this.bindEvents();
        this.setupShortcuts();
        this.initWaveform();
        console.log('🚀 智能语音转写系统已就绪');
    }

    bindEvents() {
        this.dom.startBtn.addEventListener('click', () => this.startRecording());
        this.dom.stopBtn.addEventListener('click', () => this.stopRecording());
        this.dom.clearBtn.addEventListener('click', () => this.clearResults());
        this.dom.copyAllBtn.addEventListener('click', () => this.copyAll());
        this.dom.exportBtn.addEventListener('click', () => this.exportText());

        // 帮助模态框
        this.dom.tipsToggle.addEventListener('click', () => {
            this.dom.helpModal.classList.add('active');
        });
        this.dom.closeHelpBtn.addEventListener('click', () => {
            this.dom.helpModal.classList.remove('active');
        });
        this.dom.helpModal.addEventListener('click', (e) => {
            if (e.target === this.dom.helpModal) {
                this.dom.helpModal.classList.remove('active');
            }
        });
    }

    setupShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space' && !e.target.matches('input, textarea')) {
                e.preventDefault();
                if (this.isRecording) {
                    this.stopRecording();
                } else {
                    this.startRecording();
                }
            }
        });
    }

    // ==========================================
    // 录音核心逻辑
    // ==========================================

    async startRecording() {
        if (this.isRecording) return;

        try {
            // 1. 获取麦克风权限
            this.updateStatus('connecting', '正在连接...');
            this.mediaStream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    channelCount: 1,
                    sampleRate: 16000,
                    echoCancellation: true,
                    noiseSuppression: true
                }
            });

            // 2. 连接 WebSocket
            await this.connectWebSocket();

            // 3. 初始化音频处理
            this.initAudioProcessing();

            // 4. 更新状态
            this.isRecording = true;
            this.updateUIState(true);
            this.startTimer();
            this.updateStatus('recording', '正在录音');

            // 移除空状态
            const emptyState = this.dom.resultsList.querySelector('.empty-state');
            if (emptyState) emptyState.remove();

        } catch (error) {
            console.error('启动失败:', error);
            this.updateStatus('error', '启动失败');
            alert('无法启动录音: ' + error.message);
        }
    }

    stopRecording() {
        if (!this.isRecording) return;

        this.isRecording = false;
        this.stopTimer();
        this.updateUIState(false);
        this.updateStatus('ready', '准备就绪');

        // 停止音频流
        if (this.mediaStream) {
            this.mediaStream.getTracks().forEach(track => track.stop());
        }

        // 关闭 WebSocket
        if (this.ws) {
            this.ws.send(JSON.stringify({ type: 'stop' }));
            this.ws.close();
        }

        // 关闭音频上下文
        if (this.audioContext) {
            this.audioContext.close();
        }
    }

    connectWebSocket() {
        return new Promise((resolve, reject) => {
            this.ws = new WebSocket('ws://127.0.0.1:8000/ws/voice-recognition');

            this.ws.onopen = () => resolve();
            this.ws.onerror = (err) => reject(err);

            this.ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                if (data.type === 'result') {
                    this.handleResult(data);
                }
            };
        });
    }

    initAudioProcessing() {
        const AudioContext = window.AudioContext || window.webkitAudioContext;
        this.audioContext = new AudioContext({ sampleRate: 16000 });
        const source = this.audioContext.createMediaStreamSource(this.mediaStream);

        // 分析器 (用于波形图)
        this.analyser = this.audioContext.createAnalyser();
        this.analyser.fftSize = 2048;
        source.connect(this.analyser);

        // 处理器 (用于发送数据)
        this.processor = this.audioContext.createScriptProcessor(1024, 1, 1);
        this.processor.onaudioprocess = (e) => {
            if (!this.isRecording) return;

            const inputData = e.inputBuffer.getChannelData(0);
            // 转换为PCM并发送
            const pcmData = this.floatTo16BitPCM(inputData);
            this.ws.send(JSON.stringify({
                type: 'audio',
                audio: this.arrayBufferToBase64(pcmData.buffer)
            }));
        };
        // 仅处理最终结果，忽略中间状态以保持界面整洁
        if (!data.is_final) return;
        if (!data.text || !data.text.trim()) return;

        // 修复：去除文本开头的标点符号
        let cleanText = data.text.trim().replace(/^[，。！？、,.!?]+/, '');
        if (!cleanText) return;

        const result = {
            text: cleanText,
            speaker: data.speaker,
            time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' })
        };

        this.results.push(result);
        this.renderResult(result);
    }

    renderResult(result) {
        const div = document.createElement('div');
        div.className = 'transcript-line';

        // 修复：说话人编号直接使用后端返回的值
        const speakerId = result.speaker !== null ? result.speaker : '?';

        // 颜色索引：如果后端返回1-based，需要减1来映射到0-3的颜色
        const colorIndex = (typeof result.speaker === 'number' && result.speaker > 0)
            ? result.speaker - 1
            : (result.speaker || 0);

        const speakerClass = `speaker-${colorIndex % 4}`;
        const speakerName = `说话人 ${speakerId}`;

        div.innerHTML = `
            <div class="line-meta">
                <span class="speaker-name ${speakerClass}">${speakerName}</span>
                <span class="timestamp">${result.time}</span>
            </div>
            <div class="line-content final">
                ${result.text}
            </div>
        `;

        this.dom.resultsList.appendChild(div);

        // 自动滚动到底部
        this.dom.documentArea.scrollTop = this.dom.documentArea.scrollHeight;
    }

    // ==========================================
    // 视觉效果 (横向声纹波形)
    // ==========================================

    initWaveform() {
        const canvas = this.dom.waveformCanvas;
        canvas.width = canvas.offsetWidth;
        canvas.height = canvas.offsetHeight;

        window.addEventListener('resize', () => {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        });
    }

    drawWaveform() {
        if (!this.isRecording) return;

        const canvas = this.dom.waveformCanvas;
        const ctx = canvas.getContext('2d');
        const width = canvas.width;
        const height = canvas.height;

        const bufferLength = this.analyser.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);
        this.analyser.getByteTimeDomainData(dataArray);

        ctx.clearRect(0, 0, width, height);
        ctx.lineWidth = 2;
        ctx.strokeStyle = '#3b82f6'; // 科技蓝
        ctx.beginPath();

        const sliceWidth = width * 1.0 / bufferLength;
        let x = 0;

        for (let i = 0; i < bufferLength; i++) {
            const v = dataArray[i] / 128.0;
            const y = v * height / 2;

            if (i === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }

            x += sliceWidth;
        }

        ctx.lineTo(canvas.width, canvas.height / 2);
        ctx.stroke();

        requestAnimationFrame(() => this.drawWaveform());
    }

    // ==========================================
    // 辅助功能
    // ==========================================

    startTimer() {
        this.startTime = Date.now();
        this.timerInterval = setInterval(() => {
            const diff = Date.now() - this.startTime;
            const date = new Date(diff);
            const str = date.toISOString().substr(11, 8);
            this.dom.timer.textContent = str;
        }, 1000);
    }

    stopTimer() {
        clearInterval(this.timerInterval);
    }

    updateUIState(isRecording) {
        this.dom.startBtn.disabled = isRecording;
        this.dom.stopBtn.disabled = !isRecording;

        if (isRecording) {
            this.dom.startBtn.style.display = 'none';
            this.dom.stopBtn.style.display = 'flex';
        } else {
            this.dom.startBtn.style.display = 'flex';
            this.dom.stopBtn.style.display = 'none';
        }
    }

    updateStatus(type, text) {
        this.dom.statusText.textContent = text;
        this.dom.statusDot.className = 'status-dot';
        if (type === 'recording') this.dom.statusDot.classList.add('recording');
        if (type === 'ready') this.dom.statusDot.classList.add('active');
    }

    clearResults() {
        if (confirm('确定要清空所有内容吗？')) {
            this.results = [];
            this.dom.resultsList.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">🎙️</div>
                    <h2>开始您的语音转写</h2>
                    <p>点击下方麦克风按钮，实时将语音转换为文字</p>
                </div>
            `;
            this.dom.timer.textContent = '00:00:00';
        }
    }

    copyAll() {
        const text = this.results.map(r =>
            `[${r.time}] 说话人${r.speaker !== null ? r.speaker : '?'}: ${r.text}`
        ).join('\n');

        navigator.clipboard.writeText(text).then(() => {
            alert('已复制全部内容');
        });
    }

    exportText() {
        const text = this.results.map(r =>
            `[${r.time}] 说话人${r.speaker !== null ? r.speaker : '?'}: ${r.text}`
        ).join('\n');

        const blob = new Blob([text], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `会议转写_${new Date().toISOString().slice(0, 10)}.txt`;
        a.click();
    }

    // 工具函数
    floatTo16BitPCM(input) {
        const output = new Int16Array(input.length);
        for (let i = 0; i < input.length; i++) {
            const s = Math.max(-1, Math.min(1, input[i]));
            output[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
        }
        return output;
    }

    arrayBufferToBase64(buffer) {
        let binary = '';
        const bytes = new Uint8Array(buffer);

        handleResult(data) {
            console.log('收到识别结果:', data);

            // 仅处理最终结果，忽略中间状态以保持界面整洁
            if (!data.is_final) return;
            if (!data.text || !data.text.trim()) return;

            // 修复：去除文本开头的标点符号
            let cleanText = data.text.trim().replace(/^[，。！？、,.!?]+/, '');
            if (!cleanText) return;

            const result = {
                text: cleanText,
                speaker: data.speaker,
                time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' })
            };

            this.results.push(result);
            this.renderResult(result);
        }

        renderResult(result) {
            const div = document.createElement('div');
            div.className = 'transcript-line';

            // 修复：说话人编号直接使用后端返回的值
            const speakerId = result.speaker !== null ? result.speaker : '?';

            // 颜色索引：如果后端返回1-based，需要减1来映射到0-3的颜色
            const colorIndex = (typeof result.speaker === 'number' && result.speaker > 0)
                ? result.speaker - 1
                : (result.speaker || 0);

            const speakerClass = `speaker-${colorIndex % 4}`;
            const speakerName = `说话人 ${speakerId}`;

            div.innerHTML = `
            <div class="line-meta">
                <span class="speaker-name ${speakerClass}">${speakerName}</span>
                <span class="timestamp">${result.time}</span>
            </div>
            <div class="line-content final">
                ${result.text}
            </div>
        `;

            this.dom.resultsList.appendChild(div);

            // 自动滚动到底部
            this.dom.documentArea.scrollTop = this.dom.documentArea.scrollHeight;
        }

        // ==========================================
        // 视觉效果 (横向声纹波形)
        // ==========================================

        initWaveform() {
            const canvas = this.dom.waveformCanvas;
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            window.addEventListener('resize', () => {
                canvas.width = canvas.offsetWidth;
                canvas.height = canvas.offsetHeight;
            });
        }

        drawWaveform() {
            if (!this.isRecording) return;

            const canvas = this.dom.waveformCanvas;
            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;

            const bufferLength = this.analyser.frequencyBinCount;
            const dataArray = new Uint8Array(bufferLength);
            this.analyser.getByteTimeDomainData(dataArray);

            ctx.clearRect(0, 0, width, height);
            ctx.lineWidth = 2;
            ctx.strokeStyle = '#3b82f6'; // 科技蓝
            ctx.beginPath();

            const sliceWidth = width * 1.0 / bufferLength;
            let x = 0;

            for (let i = 0; i < bufferLength; i++) {
                const v = dataArray[i] / 128.0;
                const y = v * height / 2;

                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }

                x += sliceWidth;
            }

            ctx.lineTo(canvas.width, canvas.height / 2);
            ctx.stroke();

            requestAnimationFrame(() => this.drawWaveform());
        }

        // ==========================================
        // 辅助功能
        // ==========================================

        startTimer() {
            this.startTime = Date.now();
            this.timerInterval = setInterval(() => {
                const diff = Date.now() - this.startTime;
                const date = new Date(diff);
                const str = date.toISOString().substr(11, 8);
                this.dom.timer.textContent = str;
            }, 1000);
        }

        stopTimer() {
            clearInterval(this.timerInterval);
        }

        updateUIState(isRecording) {
            this.dom.startBtn.disabled = isRecording;
            this.dom.stopBtn.disabled = !isRecording;

            if (isRecording) {
                this.dom.startBtn.style.display = 'none';
                this.dom.stopBtn.style.display = 'flex';
            } else {
                this.dom.startBtn.style.display = 'flex';
                this.dom.stopBtn.style.display = 'none';
            }
        }

        updateStatus(type, text) {
            this.dom.statusText.textContent = text;
            this.dom.statusDot.className = 'status-dot';
            if (type === 'recording') this.dom.statusDot.classList.add('recording');
            if (type === 'ready') this.dom.statusDot.classList.add('active');
        }

        clearResults() {
            if (confirm('确定要清空所有内容吗？')) {
                this.results = [];
                this.dom.resultsList.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">🎙️</div>
                    <h2>开始您的语音转写</h2>
                    <p>点击下方麦克风按钮，实时将语音转换为文字</p>
                </div>
            `;
                this.dom.timer.textContent = '00:00:00';
            }
        }

        copyAll() {
            const text = this.results.map(r =>
                `[${r.time}] 说话人${r.speaker !== null ? r.speaker : '?'}: ${r.text}`
            ).join('\n');

            navigator.clipboard.writeText(text).then(() => {
                alert('已复制全部内容');
            });
        }

        exportText() {
            const text = this.results.map(r =>
                `[${r.time}] 说话人${r.speaker !== null ? r.speaker : '?'}: ${r.text}`
            ).join('\n');

            const blob = new Blob([text], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `会议转写_${new Date().toISOString().slice(0, 10)}.txt`;
            a.click();
        }

        // 工具函数
        floatTo16BitPCM(input) {
            const output = new Int16Array(input.length);
            for (let i = 0; i < input.length; i++) {
                const s = Math.max(-1, Math.min(1, input[i]));
                output[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
            }
            return output;
        }

        arrayBufferToBase64(buffer) {
            let binary = '';
            const bytes = new Uint8Array(buffer);
            for (let i = 0; i < bytes.byteLength; i++) {
                binary += String.fromCharCode(bytes[i]);
            }
            return btoa(binary);
        }
    }

// 启动应用
document.addEventListener('DOMContentLoaded', () => {
    window.voiceTranscriber = new VoiceTranscriber();
    window.voiceprintManager = new VoiceprintManager();

    console.log('🎙️ 语音转写系统已就绪');
    console.log('🔊 声纹管理模块已加载');
});