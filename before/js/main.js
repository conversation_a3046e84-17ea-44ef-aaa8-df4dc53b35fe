const { createApp } = Vue;

// 主内容组件
const MainContent = {
    template: `
        <div class="container">
            <header class="header">
                <h1>AI 工具导航</h1>
                <p>探索人工智能的无限可能</p>
            </header>
            
            <div class="tools-grid">
                <tool-card
                    v-for="tool in tools"
                    :key="tool.id"
                    :tool="tool"
                    @click="handleToolClick(tool)"
                ></tool-card>
            </div>
        </div>
    `,
    data() {
        return {
            tools: [
                {
                    id: 1,
                    name: '图片生成',
                    icon: '🎨',
                    description: '基于AI的智能图片生成工具,输入描述即可创建精美图像',
                    status: '已上线',
                    apiEndpoint: '/api/image-generation'
                },
                {
                    id: 2,
                    name: '视频生成',
                    icon: '🎬',
                    description: '利用先进AI技术,将文字或图片转换为高质量视频内容',
                    status: '即将上线',
                    apiEndpoint: '/api/video-generation'
                },
                {
                    id: 3,
                    name: '声音识别',
                    icon: '🎤',
                    description: '高精度语音识别系统,支持多语言实时转文字',
                    status: '已上线',
                    apiEndpoint: '/api/voice-recognition'
                },
                {
                    id: 4,
                    name: '项目质量',
                    icon: '📊',
                    description: 'AI驱动的代码质量分析工具,提供智能优化建议',
                    status: '即将上线',
                    apiEndpoint: '/api/quality-check'
                }
            ]
        };
    },
    methods: {
        handleToolClick(tool) {
            console.log('点击工具:', tool.name);
            console.log('API接口:', tool.apiEndpoint);

            if (tool.id === 1) {
                window.location.href = 'image-generation.html';
                return;
            }

            if (tool.id === 3) {
                window.location.href = 'voice-recognition.html';
                return;
            }

            if (tool.id === 4) {
                window.location.href = 'project-quality.html';
                return;
            }

            alert(`${tool.name} 功能即将上线!\nAPI接口: ${tool.apiEndpoint}`);
        }
    }
};

// 工具卡片组件
const ToolCard = {
    template: `
        <div class="tool-card">
            <span class="tool-icon">{{ tool.icon }}</span>
            <h3>{{ tool.name }}</h3>
            <p>{{ tool.description }}</p>
            <span class="tool-status">{{ tool.status }}</span>
        </div>
    `,
    props: {
        tool: {
            type: Object,
            required: true
        }
    }
};

// 创建Vue应用
const app = createApp({
    components: {
        MainContent
    }
});

app.component('main-content', MainContent);
app.component('tool-card', ToolCard);

app.mount('#app');
