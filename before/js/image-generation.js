const { createApp, ref, reactive, nextTick, onMounted } = Vue;

// 模拟用户数据
const currentUser = {
    username: '用户',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=User'
};

const botUser = {
    username: 'AI助手',
    avatar: 'https://api.dicebear.com/7.x/bottts/svg?seed=AI',
    isBot: true
};

const ImageGenerationPage = {
    template: `
        <div class="discord-app">
            <!-- 1. 服务器侧边栏 (模拟) -->
            <nav class="server-sidebar">
                <div class="server-icon active" title="智能作图">
                    <i class="fas fa-image" style="color: white; font-size: 20px;"></i>
                </div>
                <div class="server-separator"></div>
                <div class="server-icon" title="返回首页" @click="goHome">
                    <i class="fas fa-home" style="color: #3ba55c; font-size: 20px;"></i>
                </div>
            </nav>

            <!-- 2. 频道侧边栏 -->
            <aside class="channel-sidebar">
                <div class="channel-header">
                    <span>智能作图</span>
                    <i class="fas fa-chevron-down" style="margin-left: auto; font-size: 12px;"></i>
                </div>
                <div class="channel-list">
                    <div class="channel-category">生图模式</div>
                    <div class="channel-item" :class="{ active: currentChannel === 'text2img' }" @click="switchChannel('text2img')">
                        <span class="channel-hash">#</span> 文生图
                    </div>
                    <div class="channel-item" :class="{ active: currentChannel === 'img2img' }" @click="switchChannel('img2img')">
                        <span class="channel-hash">#</span> 图生图
                    </div>
                    
                    <div class="channel-category">参数设置</div>
                    <div class="param-section">
                        <!-- 图片比例 -->
                        <div class="param-group">
                            <label class="param-label">图片比例</label>
                            <select v-model="params.aspectRatio" class="param-select">
                                <option value="1:1">1:1 (正方形)</option>
                                <option value="4:3">4:3 (横屏)</option>
                                <option value="3:4">3:4 (竖屏)</option>
                                <option value="16:9">16:9 (宽屏)</option>
                                <option value="9:16">9:16 (手机)</option>
                            </select>
                        </div>
                        
                        <!-- 随机种子 -->
                        <div class="param-group">
                            <label class="param-label">随机种子</label>
                            <div style="display: flex; gap: 4px;">
                                <input v-model.number="params.seed" type="number" class="param-input" placeholder="留空随机">
                                <button @click="randomSeed" class="param-btn" title="随机">
                                    <i class="fas fa-dice"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- 参考图片上传 (仅在图生图模式显示) -->
                        <div v-if="currentChannel === 'img2img'" class="param-group">
                            <label class="param-label">参考图片</label>
                            <input type="file" ref="fileInput" @change="handleFileUpload" accept="image/*" style="display: none;">
                            <button @click="$refs.fileInput.click()" class="param-upload-btn">
                                <i class="fas fa-upload"></i> 上传图片
                            </button>
                            <div v-if="params.referenceImage" class="uploaded-preview">
                                <img :src="params.referenceImage" alt="参考图">
                                <button @click="params.referenceImage = null" class="remove-img-btn">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- 3. 主聊天区域 -->
            <main class="chat-area">
                <!-- 顶部标题栏 -->
                <header class="chat-header">
                    <span class="chat-header-hash">#</span>
                    <span class="chat-header-title">{{ currentChannel === 'text2img' ? '文生图' : '图生图' }}</span>
                    <span class="chat-header-desc">{{ currentChannel === 'text2img' ? '输入文字描述生成图片' : '上传参考图并输入描述生成图片' }}</span>
                </header>

                <!-- 消息流 -->
                <div class="messages-wrapper" ref="messagesContainer">
                    <!-- 欢迎界面 (无消息时显示) -->
                    <div v-if="messages.length === 0" class="welcome-screen">
                        <div class="welcome-icon">
                            <i class="fas fa-magic" style="color: white;"></i>
                        </div>
                        <h2 class="welcome-title">欢迎使用智能作图</h2>
                        <p class="welcome-subtitle">在下方输入框中描述您想要生成的图片，AI 将为您创作</p>
                    </div>

                    <!-- 消息列表 -->
                    <div v-for="msg in messages" :key="msg.id" class="message-group">
                        <div class="avatar">
                            <img :src="msg.user.avatar" :alt="msg.user.username">
                        </div>
                        <div class="message-content-wrapper">
                            <div class="message-header">
                                <span class="username" :style="{ color: msg.user.isBot ? '#5865F2' : '#fff' }">{{ msg.user.username }}</span>
                                <span v-if="msg.user.isBot" class="bot-tag">BOT</span>
                                <span class="timestamp">{{ formatTime(msg.timestamp) }}</span>
                            </div>
                            
                            <!-- 文本内容 -->
                            <div class="message-text">
                                {{ msg.text }}
                            </div>

                            <!-- 参考图片预览 (如果有) -->
                            <div v-if="msg.referenceImage" class="ref-preview">
                                <img :src="msg.referenceImage" alt="参考图">
                            </div>

                            <!-- 图片网格 (如果是生成结果) -->
                            <div v-if="msg.images && msg.images.length > 0" class="mj-result-container">
                                <div class="mj-grid">
                                    <div v-for="(img, idx) in msg.images" :key="idx" class="mj-grid-item" @click="openImage(img)">
                                        <img :src="img" loading="lazy">
                                    </div>
                                </div>
                                <!-- 操作按钮 -->
                                <div class="mj-actions">
                                    <button class="mj-btn primary" @click="handleAction(msg, 'U1')">放大 1</button>
                                    <button class="mj-btn primary" @click="handleAction(msg, 'U2')">放大 2</button>
                                    <button class="mj-btn primary" @click="handleAction(msg, 'U3')">放大 3</button>
                                    <button class="mj-btn primary" @click="handleAction(msg, 'U4')">放大 4</button>
                                    
                                    <button class="mj-btn" @click="handleAction(msg, 'V1')">变体 1</button>
                                    <button class="mj-btn" @click="handleAction(msg, 'V2')">变体 2</button>
                                    <button class="mj-btn" @click="handleAction(msg, 'V3')">变体 3</button>
                                    <button class="mj-btn" @click="handleAction(msg, 'V4')">变体 4</button>

                                    <button class="mj-btn reroll" @click="handleReroll(msg)">
                                        <i class="fas fa-sync-alt"></i> 重新生成
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 加载指示器 (模拟正在输入) -->
                    <div v-if="isGenerating" class="message-group" style="opacity: 0.7;">
                        <div class="avatar">
                            <img :src="botUser.avatar" alt="Bot">
                        </div>
                        <div class="message-content-wrapper">
                            <div class="message-header">
                                <span class="username" style="color: #5865F2;">AI助手</span>
                                <span class="bot-tag">BOT</span>
                            </div>
                            <div class="message-text">
                                <i class="fas fa-circle-notch fa-spin"></i> 正在为 <span class="mention">@{{ currentUser.username }}</span> 生成图片...
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 底部输入框 -->
                <div class="input-area">
                    <div class="input-wrapper">
                        <input 
                            ref="promptInput"
                            v-model="promptText"
                            @keydown.enter="submitPrompt"
                            class="command-input" 
                            :placeholder="currentChannel === 'text2img' ? '描述您想要生成的图片...' : '描述您想基于参考图生成的图片...'"
                            :disabled="isGenerating"
                        >
                        <button @click="submitPrompt" class="send-btn" :disabled="!canSubmit">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </main>

            <!-- 图片预览模态框 -->
            <div class="modal-overlay" :class="{ active: showModal }" @click="showModal = false">
                <div class="modal-content" @click.stop>
                    <span class="modal-close" @click="showModal = false">&times;</span>
                    <img :src="previewImage" class="modal-image">
                </div>
            </div>
        </div>
    `,
    setup() {
        const messages = ref([]);
        const promptText = ref('');
        const isGenerating = ref(false);
        const messagesContainer = ref(null);
        const promptInput = ref(null);
        const showModal = ref(false);
        const previewImage = ref('');
        const currentChannel = ref('text2img');

        // 参数
        const params = reactive({
            aspectRatio: '1:1',
            seed: null,
            referenceImage: null
        });

        // 尺寸映射
        const aspectRatioMap = {
            '1:1': { width: 2048, height: 2048 },
            '4:3': { width: 2304, height: 1728 },
            '3:4': { width: 1728, height: 2304 },
            '16:9': { width: 2560, height: 1440 },
            '9:16': { width: 1440, height: 2560 }
        };

        // 是否可以提交
        const canSubmit = ref(true);

        // 切换频道
        const switchChannel = (channel) => {
            currentChannel.value = channel;
            // 如果切换到文生图，清除参考图片
            if (channel === 'text2img') {
                params.referenceImage = null;
            }
        };

        // 返回首页
        const goHome = () => {
            window.location.href = 'index.html';
        };

        // 随机种子
        const randomSeed = () => {
            params.seed = Math.floor(Math.random() * 2147483647);
        };

        // 文件上传
        const handleFileUpload = (event) => {
            const file = event.target.files[0];
            if (!file) return;

            if (file.size > 5 * 1024 * 1024) {
                alert('文件大小不能超过 5MB');
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                params.referenceImage = e.target.result;
            };
            reader.readAsDataURL(file);
        };

        // 自动滚动到底部
        const scrollToBottom = () => {
            nextTick(() => {
                if (messagesContainer.value) {
                    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
                }
            });
        };

        // 格式化时间
        const formatTime = (date) => {
            const d = new Date(date);
            return d.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        };

        // 提交 Prompt
        const submitPrompt = async () => {
            const text = promptText.value.trim();
            if (!text || isGenerating.value) return;

            // 图生图模式下，必须有参考图
            if (currentChannel.value === 'img2img' && !params.referenceImage) {
                alert('请先上传参考图片');
                return;
            }

            // 1. 添加用户消息
            messages.value.push({
                id: Date.now(),
                user: currentUser,
                text: text,
                referenceImage: currentChannel.value === 'img2img' ? params.referenceImage : null,
                timestamp: new Date()
            });

            promptText.value = '';
            isGenerating.value = true;
            scrollToBottom();

            try {
                // 获取尺寸
                const size = aspectRatioMap[params.aspectRatio];

                // 2. 调用 API
                const requestBody = {
                    prompt: text,
                    width: size.width,
                    height: size.height,
                    watermark: false
                };

                // 添加种子
                if (params.seed !== null && params.seed !== '') {
                    requestBody.seed = params.seed;
                }

                console.log('生成参数:', requestBody);

                const response = await fetch('http://localhost:8000/api/image-generation', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(requestBody)
                });

                const result = await response.json();

                if (result.success && result.data) {
                    const imgUrl = result.data.image_url;
                    // 模拟 4 张图 (实际项目中后端应该返回 4 张不同的图)
                    const images = [imgUrl, imgUrl, imgUrl, imgUrl];

                    // 3. 添加 Bot 回复
                    messages.value.push({
                        id: Date.now() + 1,
                        user: botUser,
                        text: `已为您生成图片`,
                        images: images,
                        timestamp: new Date()
                    });
                } else {
                    throw new Error(result.message || '生成失败');
                }

            } catch (error) {
                console.error(error);
                messages.value.push({
                    id: Date.now() + 1,
                    user: botUser,
                    text: `❌ 生成失败: ${error.message}`,
                    timestamp: new Date()
                });
            } finally {
                isGenerating.value = false;
                scrollToBottom();
                nextTick(() => promptInput.value?.focus());
            }
        };

        // 处理按钮点击
        const handleAction = (msg, action) => {
            alert(`${action} 功能开发中`);
        };

        // 重新生成
        const handleReroll = (msg) => {
            alert('重新生成功能开发中');
        };

        // 查看大图
        const openImage = (url) => {
            previewImage.value = url;
            showModal.value = true;
        };

        onMounted(() => {
            promptInput.value?.focus();
        });

        return {
            messages,
            promptText,
            isGenerating,
            messagesContainer,
            promptInput,
            showModal,
            previewImage,
            currentUser,
            botUser,
            currentChannel,
            params,
            canSubmit,
            switchChannel,
            goHome,
            randomSeed,
            handleFileUpload,
            submitPrompt,
            formatTime,
            handleAction,
            handleReroll,
            openImage
        };
    }
};

const app = createApp({
    components: { ImageGenerationPage }
});

app.component('image-generation-page', ImageGenerationPage);
app.mount('#app');
