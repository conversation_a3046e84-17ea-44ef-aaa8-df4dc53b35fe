/**
 * 声纹管理模块
 * 负责声纹录入、查询、删除、阈值调整等功能
 */
class VoiceprintManager {
    constructor() {
        this.API_BASE = 'http://localhost:8000/api/voiceprint';
        this.voiceprints = [];
        this.matchThreshold = 0.6;
        this.isRecording = false;
        this.recordedAudio = null;
        this.mediaRecorder = null;

        // DOM元素
        this.dom = {
            voiceprintBtn: document.getElementById('voiceprintManage'),
            modal: document.getElementById('voiceprintModal'),
            closeBtn: document.getElementById('closeVoiceprintBtn'),
            thresholdSlider: document.getElementById('thresholdSlider'),
            thresholdLabel: document.getElementById('thresholdLabel'),
            voiceprintList: document.getElementById('voiceprintList'),
            showRecorderBtn: document.getElementById('showRecorderBtn'),
            recorderPanel: document.getElementById('recorderPanel'),
            userNameInput: document.getElementById('userNameInput'),
            recordBtn: document.getElementById('recordVoiceprintBtn'),
            recordBtnText: document.getElementById('recordBtnText'),
            recordingStatus: document.getElementById('recordingStatus'),
            cancelRecordBtn: document.getElementById('cancelRecordBtn'),
            confirmBtn: document.getElementById('confirmVoiceprintBtn')
        };

        this.init();
    }

    init() {
        this.bindEvents();
        this.loadThreshold();
    }

    bindEvents() {
        // 打开/关闭模态框
        this.dom.voiceprintBtn.addEventListener('click', () => this.openModal());
        this.dom.closeBtn.addEventListener('click', () => this.closeModal());
        this.dom.modal.addEventListener('click', (e) => {
            if (e.target === this.dom.modal) this.closeModal();
        });

        // 阈值调整
        this.dom.thresholdSlider.addEventListener('input', (e) => {
            this.matchThreshold = parseFloat(e.target.value);
            this.dom.thresholdLabel.textContent = `匹配阈值 (${this.matchThreshold})`;
        });

        this.dom.thresholdSlider.addEventListener('change', () => {
            this.updateThreshold();
        });

        // 显示/隐藏录音面板
        this.dom.showRecorderBtn.addEventListener('click', () => {
            this.dom.recorderPanel.style.display = 'block';
            this.dom.showRecorderBtn.style.display = 'none';
        });

        // 录音控制
        this.dom.recordBtn.addEventListener('click', () => this.toggleRecording());
        this.dom.cancelRecordBtn.addEventListener('click', () => this.cancelRecording());
        this.dom.confirmBtn.addEventListener('click', () => this.confirmVoiceprint());
    }

    async openModal() {
        this.dom.modal.classList.add('active');
        await this.loadVoiceprints();
    }

    closeModal() {
        this.dom.modal.classList.remove('active');
        this.resetRecorder();
    }

    // ==========================================
    // API 调用
    // ==========================================

    async loadThreshold() {
        try {
            const response = await fetch(`${this.API_BASE}/threshold`);
            const result = await response.json();
            if (result.success) {
                this.matchThreshold = result.data.threshold;
                this.dom.thresholdSlider.value = this.matchThreshold;
                this.dom.thresholdLabel.textContent = `匹配阈值 (${this.matchThreshold})`;
            }
        } catch (error) {
            console.error('加载阈值失败:', error);
        }
    }

    async updateThreshold() {
        try {
            const response = await fetch(`${this.API_BASE}/threshold`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ threshold: this.matchThreshold })
            });
            const result = await response.json();
            if (result.success) {
                console.log('✅ 阈值已更新:', this.matchThreshold);
            }
        } catch (error) {
            console.error('更新阈值失败:', error);
            alert('更新阈值失败');
        }
    }

    async loadVoiceprints() {
        try {
            const response = await fetch(`${this.API_BASE}/list`);
            const result = await response.json();
            if (result.success) {
                this.voiceprints = result.data;
                this.renderVoiceprints();
            }
        } catch (error) {
            console.error('加载声纹列表失败:', error);
        }
    }

    renderVoiceprints() {
        const container = this.dom.voiceprintList;
        const listHeader = '<div class="list-header">已录入声纹</div>';

        if (this.voiceprints.length === 0) {
            container.innerHTML = listHeader + '<div class="empty-voiceprints">暂无声纹，点击下方按钮录入</div>';
            return;
        }

        const items = this.voiceprints.map(vp => `
            <div class="voiceprint-item">
                <div class="vp-avatar">${vp.user_name[0]}</div>
                <div class="vp-info">
                    <div class="vp-name">${vp.user_name}</div>
                    <div class="vp-time">${this.formatTime(vp.created_at)}</div>
                </div>
                <button class="vp-delete-btn" data-id="${vp.feature_id}" title="删除">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="3 6 5 6 21 6"></polyline>
                        <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                    </svg>
                </button>
            </div>
        `).join('');

        container.innerHTML = listHeader + items;

        // 绑定删除事件
        container.querySelectorAll('.vp-delete-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const featureId = btn.dataset.id;
                this.deleteVoiceprint(featureId);
            });
        });
    }

    async deleteVoiceprint(featureId) {
        if (!confirm('确定要删除此声纹吗？')) return;

        try {
            const response = await fetch(`${this.API_BASE}/delete/${featureId}`, {
                method: 'DELETE'
            });
            const result = await response.json();
            if (result.success) {
                alert('删除成功');
                await this.loadVoiceprints();
            } else {
                alert('删除失败: ' + result.message);
            }
        } catch (error) {
            console.error('删除声纹失败:', error);
            alert('删除失败');
        }
    }

    // ==========================================
    // 录音功能
    // ==========================================

    async toggleRecording() {
        if (this.isRecording) {
            this.stopRecording();
        } else {
            await this.startRecording();
        }
    }

    async startRecording() {
        try {
            // 请求16kHz采样率的音频流
            const stream = await navigator.mediaDevices.getUserMedia({ 
                audio: {
                    channelCount: 1,
                    sampleRate: 16000,
                    echoCancellation: true,
                    noiseSuppression: true
                }
            });
            
            this.mediaRecorder = new MediaRecorder(stream);
            this.audioChunks = [];

            this.mediaRecorder.ondataavailable = (event) => {
                this.audioChunks.push(event.data);
            };

            this.mediaRecorder.onstop = () => {
                const audioBlob = new Blob(this.audioChunks, { type: 'audio/webm' });
                this.convertToMP3(audioBlob);
            };

            this.mediaRecorder.start();
            this.isRecording = true;

            // UI更新
            this.dom.recordBtn.classList.add('recording');
            this.dom.recordBtnText.textContent = '停止录音';
            this.dom.recordingStatus.textContent = '🔴 正在录音...';

            // 10秒后自动停止
            setTimeout(() => {
                if (this.isRecording) {
                    this.stopRecording();
                }
            }, 10000);

        } catch (error) {
            console.error('录音失败:', error);
            alert('无法访问麦克风');
        }
    }

    stopRecording() {
        if (this.mediaRecorder && this.isRecording) {
            this.mediaRecorder.stop();
            this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
            this.isRecording = false;

            // UI更新
            this.dom.recordBtn.classList.remove('recording');
            this.dom.recordBtnText.textContent = '重新录音';
            this.dom.recordingStatus.textContent = '✅ 录音完成';
        }
    }

    async convertToMP3(blob) {
        try {
            this.dom.recordingStatus.textContent = '⏳ 正在处理音频...';
            
            // 创建AudioContext处理音频
            const audioContext = new (window.AudioContext || window.webkitAudioContext)({
                sampleRate: 16000
            });
            
            // 读取Blob为ArrayBuffer
            const arrayBuffer = await blob.arrayBuffer();
            
            // 解码音频数据
            const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
            
            // 获取单声道音频数据
            const samples = audioBuffer.getChannelData(0);
            
            // 转换为16位PCM
            const pcmData = new Int16Array(samples.length);
            for (let i = 0; i < samples.length; i++) {
                const s = Math.max(-1, Math.min(1, samples[i]));
                pcmData[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
            }
            
            // 使用lamejs编码为MP3
            const mp3encoder = new lamejs.Mp3Encoder(1, 16000, 128);
            const mp3Data = [];
            
            const blockSize = 1152;
            for (let i = 0; i < pcmData.length; i += blockSize) {
                const chunk = pcmData.subarray(i, Math.min(i + blockSize, pcmData.length));
                const mp3buf = mp3encoder.encodeBuffer(chunk);
                if (mp3buf.length > 0) {
                    mp3Data.push(mp3buf);
                }
            }
            
            // 完成编码
            const mp3buf = mp3encoder.flush();
            if (mp3buf.length > 0) {
                mp3Data.push(mp3buf);
            }
            
            // 合并所有MP3数据
            const mp3Blob = new Blob(mp3Data, { type: 'audio/mp3' });
            
            // 转为Base64
            const reader = new FileReader();
            reader.onloadend = () => {
                this.recordedAudio = reader.result.split(',')[1];
                this.dom.confirmBtn.disabled = false;
                this.dom.recordingStatus.textContent = '✅ 录音完成，可以确认录入';
                console.log('✅ MP3转换成功，大小:', this.recordedAudio.length, 'bytes');
            };
            reader.readAsDataURL(mp3Blob);
            
        } catch (error) {
            console.error('❌ MP3转换失败:', error);
            this.dom.recordingStatus.textContent = '❌ 音频处理失败';
            alert('音频处理失败，请重新录音');
        }
    }

    async confirmVoiceprint() {
        const userName = this.dom.userNameInput.value.trim();
        if (!userName) {
            alert('请输入姓名');
            return;
        }
        if (!this.recordedAudio) {
            alert('请先录音');
            return;
        }

        try {
            const response = await fetch(`${this.API_BASE}/register`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    user_name: userName,
                    audio_data: this.recordedAudio
                })
            });

            const result = await response.json();
            if (result.success) {
                alert(`✅ 声纹注册成功！\n姓名: ${userName}`);
                this.resetRecorder();
                await this.loadVoiceprints();
            } else {
                alert('注册失败: ' + result.message);
            }
        } catch (error) {
            console.error('注册声纹失败:', error);
            alert('注册失败');
        }
    }

    cancelRecording() {
        this.resetRecorder();
    }

    resetRecorder() {
        this.dom.recorderPanel.style.display = 'none';
        this.dom.showRecorderBtn.style.display = 'block';
        this.dom.userNameInput.value = '';
        this.dom.recordBtnText.textContent = '点击录音 (10秒)';
        this.dom.recordingStatus.textContent = '';
        this.dom.confirmBtn.disabled = true;
        this.dom.recordBtn.classList.remove('recording');
        this.recordedAudio = null;
        if (this.isRecording) {
            this.stopRecording();
        }
    }

    // ==========================================
    // 辅助函数
    // ==========================================

    formatTime(dateStr) {
        if (!dateStr) return '';
        const date = new Date(dateStr);
        return date.toLocaleString('zh-CN', {
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
}

// 导出给voice-recognition.js使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = VoiceprintManager;
}
