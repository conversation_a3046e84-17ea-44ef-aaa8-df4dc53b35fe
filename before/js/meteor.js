// 流星动画实现
class MeteorAnimation {
    constructor() {
        this.canvas = document.getElementById('meteor-canvas');
        this.ctx = this.canvas.getContext('2d');
        this.meteors = [];
        this.init();
    }

    init() {
        this.resize();
        window.addEventListener('resize', () => this.resize());
        this.createMeteors();
        this.animate();
    }

    resize() {
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;
    }

    createMeteors() {
        // 初始创建5颗流星
        for (let i = 0; i < 5; i++) {
            this.addMeteor();
        }
    }

    addMeteor() {
        const meteor = {
            x: Math.random() * this.canvas.width,
            y: -50,
            length: Math.random() * 80 + 50,
            speed: Math.random() * 3 + 2,
            size: Math.random() * 2 + 1,
            angle: Math.PI / 4 + Math.random() * 0.2,
            opacity: Math.random() * 0.5 + 0.5
        };
        this.meteors.push(meteor);
    }

    drawMeteor(meteor) {
        this.ctx.save();
        
        // 创建渐变
        const gradient = this.ctx.createLinearGradient(
            meteor.x,
            meteor.y,
            meteor.x - Math.cos(meteor.angle) * meteor.length,
            meteor.y - Math.sin(meteor.angle) * meteor.length
        );
        
        gradient.addColorStop(0, `rgba(255, 255, 255, ${meteor.opacity})`);
        gradient.addColorStop(0.5, `rgba(147, 197, 253, ${meteor.opacity * 0.6})`);
        gradient.addColorStop(1, 'rgba(147, 197, 253, 0)');
        
        this.ctx.strokeStyle = gradient;
        this.ctx.lineWidth = meteor.size;
        this.ctx.lineCap = 'round';
        
        this.ctx.beginPath();
        this.ctx.moveTo(meteor.x, meteor.y);
        this.ctx.lineTo(
            meteor.x - Math.cos(meteor.angle) * meteor.length,
            meteor.y - Math.sin(meteor.angle) * meteor.length
        );
        this.ctx.stroke();
        
        this.ctx.restore();
    }

    updateMeteor(meteor) {
        meteor.x += Math.cos(meteor.angle) * meteor.speed;
        meteor.y += Math.sin(meteor.angle) * meteor.speed;
    }

    animate() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 随机添加新流星
        if (Math.random() < 0.03 && this.meteors.length < 10) {
            this.addMeteor();
        }
        
        // 更新和绘制所有流星
        for (let i = this.meteors.length - 1; i >= 0; i--) {
            const meteor = this.meteors[i];
            this.drawMeteor(meteor);
            this.updateMeteor(meteor);
            
            // 移除超出屏幕的流星
            if (meteor.x > this.canvas.width + 100 || meteor.y > this.canvas.height + 100) {
                this.meteors.splice(i, 1);
            }
        }
        
        requestAnimationFrame(() => this.animate());
    }
}

// 页面加载完成后初始化
window.addEventListener('DOMContentLoaded', () => {
    new MeteorAnimation();
});
