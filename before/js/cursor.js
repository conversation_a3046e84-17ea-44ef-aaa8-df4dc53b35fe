// 鼠标光点拖尾效果
class CursorTrail {
    constructor() {
        this.canvas = document.getElementById('cursor-canvas');
        this.ctx = this.canvas.getContext('2d');
        this.particles = [];
        this.mouse = { x: 0, y: 0 };
        this.init();
    }

    init() {
        this.resize();
        window.addEventListener('resize', () => this.resize());
        window.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        this.animate();
    }

    resize() {
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;
    }

    handleMouseMove(e) {
        this.mouse.x = e.clientX;
        this.mouse.y = e.clientY;
        
        // 添加新粒子
        for (let i = 0; i < 3; i++) {
            this.addParticle();
        }
    }

    addParticle() {
        const particle = {
            x: this.mouse.x + (Math.random() - 0.5) * 10,
            y: this.mouse.y + (Math.random() - 0.5) * 10,
            size: Math.random() * 3 + 1,
            speedX: (Math.random() - 0.5) * 2,
            speedY: (Math.random() - 0.5) * 2,
            life: 1,
            decay: Math.random() * 0.01 + 0.015,
            color: {
                r: Math.random() * 100 + 155,
                g: Math.random() * 100 + 155,
                b: 255
            }
        };
        this.particles.push(particle);
    }

    drawParticle(particle) {
        this.ctx.save();
        
        // 创建径向渐变
        const gradient = this.ctx.createRadialGradient(
            particle.x, particle.y, 0,
            particle.x, particle.y, particle.size
        );
        
        gradient.addColorStop(0, `rgba(${particle.color.r}, ${particle.color.g}, ${particle.color.b}, ${particle.life})`);
        gradient.addColorStop(1, `rgba(${particle.color.r}, ${particle.color.g}, ${particle.color.b}, 0)`);
        
        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        this.ctx.fill();
        
        this.ctx.restore();
    }

    updateParticle(particle) {
        particle.x += particle.speedX;
        particle.y += particle.speedY;
        particle.life -= particle.decay;
        particle.size *= 0.98;
    }

    animate() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 更新和绘制所有粒子
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];
            this.drawParticle(particle);
            this.updateParticle(particle);
            
            // 移除生命值耗尽的粒子
            if (particle.life <= 0 || particle.size < 0.5) {
                this.particles.splice(i, 1);
            }
        }
        
        requestAnimationFrame(() => this.animate());
    }
}

// 页面加载完成后初始化
window.addEventListener('DOMContentLoaded', () => {
    new CursorTrail();
});
