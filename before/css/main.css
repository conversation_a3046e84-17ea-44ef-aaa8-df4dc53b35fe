* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 
                 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 50%, #0f1419 100%);
    min-height: 100vh;
    overflow-x: hidden;
    color: #fff;
    position: relative;
}

/* 画布样式 */
#meteor-canvas,
#cursor-canvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

#cursor-canvas {
    z-index: 2;
}

/* 主容器 */
#app {
    position: relative;
    z-index: 3;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 头部 */
.header {
    padding: 60px 0 40px;
    text-align: center;
}

.header h1 {
    font-size: 56px;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 16px;
    letter-spacing: 2px;
}

.header p {
    font-size: 20px;
    color: #a0aec0;
    font-weight: 300;
}

/* 工具网格 */
.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    padding: 40px 0 80px;
}

/* 工具卡片 */
.tool-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 80px 30px;  /* 增加上下padding到80px,原来是40px */
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    min-height: 400px;  /* 设置最小高度确保卡片足够高 */
}

.tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.tool-card:hover {
    transform: translateY(-10px);
    border-color: rgba(102, 126, 234, 0.5);
    box-shadow: 0 20px 60px rgba(102, 126, 234, 0.3);
}

.tool-card:hover::before {
    opacity: 1;
}

.tool-icon {
    font-size: 64px;
    margin-bottom: 20px;
    display: block;
    position: relative;
    z-index: 1;
}

.tool-card h3 {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 12px;
    position: relative;
    z-index: 1;
}

.tool-card p {
    font-size: 16px;
    color: #a0aec0;
    line-height: 1.6;
    position: relative;
    z-index: 1;
}

.tool-status {
    display: inline-block;
    margin-top: 16px;
    padding: 6px 16px;
    background: rgba(102, 126, 234, 0.2);
    border: 1px solid rgba(102, 126, 234, 0.4);
    border-radius: 20px;
    font-size: 13px;
    color: #667eea;
    position: relative;
    z-index: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header h1 {
        font-size: 36px;
    }
    
    .header p {
        font-size: 16px;
    }
    
    .tools-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .tool-card {
        padding: 60px 20px;  /* 移动端也增加padding */
        min-height: 350px;
    }
}
