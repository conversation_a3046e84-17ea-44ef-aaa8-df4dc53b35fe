/* ================================
   声纹管理模态框样式
   ================================ */

/* 参会人数配置 */
.config-group {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: 16px;
    opacity: 1;
    transition: opacity 0.3s;
}

.config-group.hidden {
    opacity: 0;
    pointer-events: none;
}

.config-label {
    font-size: 12px;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 4px;
}

.config-select {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    color: var(--text-primary);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    outline: none;
    cursor: pointer;
}

.config-select:hover {
    background: rgba(59, 130, 246, 0.2);
}

.config-checkbox {
    margin: 0;
    cursor: pointer;
}

/* 声纹管理模态框 */
.modal-voiceprint {
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
}

.threshold-section {
    background: rgba(59, 130, 246, 0.1);
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.threshold-label {
    display: block;
    font-size: 13px;
    color: var(--text-secondary);
    margin-bottom: 8px;
}

.threshold-slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: rgba(255, 255, 255, 0.1);
    outline: none;
    -webkit-appearance: none;
}

.threshold-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--accent-color);
    cursor: pointer;
}

.threshold-slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--accent-color);
    cursor: pointer;
    border: none;
}

.threshold-tips {
    display: flex;
    justify-content: space-between;
    font-size: 11px;
    color: var(--text-secondary);
    margin-top: 4px;
}

/* 声纹列表 */
.voiceprint-list {
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 16px;
}

.list-header {
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 8px;
    font-weight: 600;
}

.empty-voiceprints {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary);
    font-size: 13px;
}

.voiceprint-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    margin-bottom: 8px;
    transition: background 0.2s;
}

.voiceprint-item:hover {
    background: rgba(255, 255, 255, 0.08);
}

.vp-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 600;
    color: white;
}

.vp-info {
    flex: 1;
}

.vp-name {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 2px;
}

.vp-time {
    font-size: 11px;
    color: var(--text-secondary);
}

.vp-delete-btn {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    background: rgba(239, 68, 68, 0.1);
    border: none;
    color: var(--danger-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.vp-delete-btn:hover {
    background: rgba(239, 68, 68, 0.2);
    transform: scale(1.1);
}

/* 添加声纹 */
.add-voiceprint-section {
    border-top: 1px solid var(--border-color);
    padding-top: 16px;
}

.add-vp-btn {
    width: 100%;
    padding: 12px;
    background: var(--accent-color);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: background 0.2s;
}

.add-vp-btn:hover {
    background: var(--accent-hover);
}

.add-vp-btn svg {
    stroke: currentColor;
    stroke-width: 2;
}

/* 录音面板 */
.recorder-panel {
    margin-top: 16px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.recorder-input-group {
    margin-bottom: 12px;
}

.recorder-input-group label {
    display: block;
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 6px;
}

.vp-input {
    width: 100%;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 14px;
    outline: none;
}

.vp-input:focus {
    border-color: var(--accent-color);
}

.recorder-controls {
    text-align: center;
    margin-bottom: 12px;
}

.record-btn {
    padding: 12px 24px;
    background: var(--danger-color);
    color: white;
    border: none;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s;
}

.record-btn:hover {
    transform: scale(1.05);
}

.record-btn.recording {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }
}

.recording-status {
    margin-top: 8px;
    font-size: 12px;
    color: var(--accent-color);
    min-height: 18px;
}

.recorder-actions {
    display: flex;
    gap: 8px;
}

.secondary-btn,
.primary-btn {
    flex: 1;
    padding: 10px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
}

.secondary-btn {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.secondary-btn:hover {
    background: rgba(255, 255, 255, 0.15);
}

.primary-btn {
    background: var(--accent-color);
    color: white;
}

.primary-btn:hover:not(:disabled) {
    background: var(--accent-hover);
}

.primary-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}