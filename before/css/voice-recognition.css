/* ================================
   智能语音转写 - 专业版样式 (讯飞风格)
   ================================ */

:root {
    --bg-color: #0f172a;
    --text-primary: #f8fafc;
    --text-secondary: #94a3b8;
    --accent-color: #3b82f6;
    --accent-hover: #2563eb;
    --danger-color: #ef4444;
    --panel-bg: rgba(30, 41, 59, 0.7);
    --border-color: rgba(255, 255, 255, 0.1);
    --glass-bg: rgba(15, 23, 42, 0.85);
}

body {
    background-color: var(--bg-color);
    color: var(--text-primary);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Aria<PERSON>, sans-serif;
    margin: 0;
    padding: 0;
    overflow: hidden;
    /* 防止整体滚动，只让文档区域滚动 */
    height: 100vh;  /* 确保body占满视口 */
}

/* 移除主页的流星背景影响，如果有的话 */
#app.pro-interface {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: radial-gradient(circle at 50% 0%, #1e293b 0%, #0f172a 100%);
    overflow: hidden;  /* 防止app容器滚动 */
}

/* ================================
   顶部导航
   ================================ */
.pro-header {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    border-bottom: 1px solid var(--border-color);
    background: rgba(15, 23, 42, 0.5);
    backdrop-filter: blur(10px);
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.back-link {
    display: flex;
    align-items: center;
    gap: 6px;
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 14px;
    transition: color 0.2s;
}

.back-link:hover {
    color: var(--text-primary);
}

.divider {
    width: 1px;
    height: 16px;
    background: var(--border-color);
}

.app-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.status-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    font-size: 12px;
    color: var(--text-secondary);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--text-secondary);
    transition: all 0.3s;
}

.status-dot.active {
    background: #4ade80;
    box-shadow: 0 0 8px #4ade80;
}

.status-dot.recording {
    background: #ef4444;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }

    100% {
        opacity: 1;
    }
}

/* ================================
   核心文档区域
   ================================ */
.document-area {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;  /* 防止横向滚动 */
    padding: 40px 0 180px;
    /* 底部留出控制台空间 */
    scroll-behavior: smooth;
    /* 确保有明确的高度约束 */
    min-height: 0;
}

.document-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 24px;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding-top: 100px;
    animation: fadeIn 0.5s ease-out;
}

.empty-icon {
    font-size: 64px;
    margin-bottom: 24px;
    opacity: 0.8;
}

.empty-state h2 {
    font-size: 24px;
    margin-bottom: 12px;
    font-weight: 600;
}

.empty-state p {
    color: var(--text-secondary);
    margin-bottom: 48px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
    max-width: 600px;
    margin: 0 auto;
}

.feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    padding: 24px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 16px;
    border: 1px solid var(--border-color);
}

.feature-item .icon {
    font-size: 24px;
}

.feature-item span:last-child {
    font-size: 14px;
    color: var(--text-secondary);
}

/* 转写条目 (文档流样式) */
.transcript-line {
    display: flex;
    gap: 20px;
    margin-bottom: 32px;
    animation: slideUp 0.3s ease-out;
}

.line-meta {
    flex-shrink: 0;
    width: 100px;
    text-align: right;
    padding-top: 4px;
}

.speaker-name {
    display: block;
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 4px;
}

/* 说话人颜色 */
.speaker-0 {
    color: #60a5fa;
}

.speaker-1 {
    color: #f472b6;
}

.speaker-2 {
    color: #4ade80;
}

.speaker-3 {
    color: #fbbf24;
}

.timestamp {
    display: block;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.3);
    font-family: monospace;
}

.line-content {
    flex: 1;
    font-size: 16px;
    line-height: 1.8;
    color: var(--text-primary);
    text-align: justify;
}

.line-content.final {
    color: var(--text-primary);
}

.line-content.interim {
    color: var(--text-secondary);
    font-style: italic;
}

/* ================================
   底部控制台
   ================================ */
.control-console {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-top: 1px solid var(--border-color);
    z-index: 100;
    padding-bottom: env(safe-area-inset-bottom);
}

/* 声纹波形 */
.waveform-container {
    height: 60px;
    width: 100%;
    position: absolute;
    top: -60px;
    left: 0;
    background: linear-gradient(to top, rgba(15, 23, 42, 0.9), transparent);
    pointer-events: none;
}

#waveformCanvas {
    width: 100%;
    height: 100%;
}

/* 控制栏 */
.control-bar {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 40px;
    max-width: 1200px;
    margin: 0 auto;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
}

.control-group.center {
    justify-content: center;
    flex: 0 0 auto;
}

.control-group.right {
    justify-content: flex-end;
}

/* 计时器 */
.timer {
    font-family: monospace;
    font-size: 16px;
    color: var(--text-primary);
    background: rgba(0, 0, 0, 0.2);
    padding: 6px 12px;
    border-radius: 4px;
    letter-spacing: 1px;
}

/* 主操作按钮 */
.main-action-btn {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    background: var(--accent-color);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.main-action-btn:hover {
    transform: scale(1.05);
    background: var(--accent-hover);
}

.main-action-btn:active {
    transform: scale(0.95);
}

.main-action-btn.stop {
    background: rgba(255, 255, 255, 0.1);
    width: 48px;
    height: 48px;
}

.main-action-btn.stop:hover {
    background: rgba(239, 68, 68, 0.2);
}

.main-action-btn.stop .stop-square {
    width: 16px;
    height: 16px;
    background: #ef4444;
    border-radius: 2px;
}

.main-action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* 工具按钮 */
.tool-btn,
.icon-btn {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.tool-btn:hover,
.icon-btn:hover {
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
}

/* 滚动条 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* 动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 模态框 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 1000;
    display: none;
    align-items: center;
    justify-content: center;
}

.modal-overlay.active {
    display: flex;
    animation: fadeIn 0.2s;
}

.modal-content {
    background: #1e293b;
    border: 1px solid var(--border-color);
    border-radius: 16px;
    width: 90%;
    max-width: 400px;
    padding: 24px;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
}

.close-modal {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 24px;
    cursor: pointer;
}

.help-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.help-list li {
    display: flex;
    gap: 16px;
    align-items: flex-start;
}

.help-list .icon {
    font-size: 20px;
    background: rgba(255, 255, 255, 0.05);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
}

.help-list h4 {
    margin: 0 0 4px 0;
    font-size: 15px;
}

.help-list p {
    margin: 0;
    font-size: 13px;
    color: var(--text-secondary);
}

/* 移动端适配 */
@media (max-width: 768px) {
    .control-bar {
        padding: 0 16px;
    }

    .timer {
        font-size: 14px;
    }

    .transcript-line {
        flex-direction: column;
        gap: 8px;
    }

    .line-meta {
        width: 100%;
        text-align: left;
        display: flex;
        gap: 10px;
        align-items: center;
    }

    .document-content {
        padding: 0 16px;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }
}