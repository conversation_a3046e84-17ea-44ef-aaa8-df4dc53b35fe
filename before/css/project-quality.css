:root {
    --bg-color: #202020;
    --grid-color: #303030;
    --node-bg: #353535;
    --node-header: #2a2a2a;
    --text-color: #e0e0e0;
    --accent-color: #606060;
    --link-color: #a0a0a0;
    --input-color: #1a1a1a;
}

body,
html {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: var(--bg-color);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--text-color);
}

#app {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* 菜单栏 */
.menu-bar {
    height: 30px;
    background-color: #2d2d2d;
    display: flex;
    align-items: center;
    padding: 0 10px;
    border-bottom: 1px solid #404040;
    user-select: none;
}

.menu-item {
    padding: 5px 10px;
    cursor: pointer;
    font-size: 14px;
}

.menu-item:hover {
    background-color: #404040;
}

.spacer {
    flex: 1;
}

.action-btn {
    background-color: #404040;
    margin-left: 5px;
    border-radius: 3px;
}

.action-btn.secondary {
    background-color: #303030;
}

/* 工作区 */
.workspace {
    flex: 1;
    position: relative;
    overflow: hidden;
    cursor: grab;
}

.workspace:active {
    cursor: grabbing;
}

/* 背景网格 - 使用 CSS 渐变模拟 */
.workspace::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(var(--grid-color) 1px, transparent 1px),
        linear-gradient(90deg, var(--grid-color) 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.5;
    pointer-events: none;
    z-index: 0;
}

#connections-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
    /* 让鼠标事件穿透到下面的节点 */
}

#nodes-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    pointer-events: none;
    /* 容器本身不阻挡，子元素节点开启 pointer-events */
}

/* 节点样式 */
.node {
    position: absolute;
    background-color: var(--node-bg);
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
    min-width: 200px;
    border: 1px solid #444;
    pointer-events: auto;
    display: flex;
    flex-direction: column;
}

.node-header {
    background-color: var(--node-header);
    padding: 8px 12px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    font-weight: bold;
    font-size: 14px;
    cursor: move;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.node-status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #4caf50;
    /* 绿色表示正常 */
}

.node-body {
    padding: 10px;
}

/* 端口样式 */
.io-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
    position: relative;
    height: 24px;
}

.input-port,
.output-port {
    width: 12px;
    height: 12px;
    background-color: #777;
    border-radius: 50%;
    cursor: crosshair;
}

.input-port:hover,
.output-port:hover {
    background-color: #fff;
}

.input-label {
    margin-left: 5px;
    font-size: 12px;
    color: #ccc;
}

.output-label {
    margin-right: 5px;
    font-size: 12px;
    color: #ccc;
    text-align: right;
    flex: 1;
}

/* 控件样式 */
.widget-row {
    margin-top: 8px;
}

.widget-label {
    display: block;
    font-size: 12px;
    color: #aaa;
    margin-bottom: 2px;
}

.node input[type="text"],
.node input[type="number"],
.node select,
.node textarea {
    width: 100%;
    background-color: var(--input-color);
    border: 1px solid #555;
    color: #eee;
    padding: 4px;
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 12px;
}

.node button {
    width: 100%;
    background-color: #444;
    border: none;
    color: #eee;
    padding: 6px;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 5px;
}

.node button:hover {
    background-color: #555;
}

/* 控制面板 */
.control-panel {
    position: absolute;
    right: 10px;
    bottom: 10px;
    width: 200px;
    background-color: #2d2d2d;
    padding: 10px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    z-index: 100;
}

.control-panel button {
    display: block;
    width: 100%;
    margin-bottom: 5px;
    padding: 8px;
    background-color: #444;
    border: none;
    color: white;
    border-radius: 4px;
    cursor: pointer;
}

.control-panel button:hover {
    background-color: #555;
}