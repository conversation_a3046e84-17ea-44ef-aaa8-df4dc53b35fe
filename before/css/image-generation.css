/* Discord/Midjourney 风格样式重构 */

:root {
    --discord-bg-primary: #36393f;
    --discord-bg-secondary: #2f3136;
    --discord-bg-tertiary: #202225;
    --discord-channel-list: #2f3136;
    --discord-user-list: #2f3136;
    --discord-header: #36393f;
    --discord-text-normal: #dcddde;
    --discord-text-muted: #72767d;
    --discord-text-header: #ffffff;
    --discord-interactive-normal: #b9bbbe;
    --discord-interactive-hover: #dcddde;
    --discord-interactive-active: #ffffff;
    --discord-brand: #5865F2;
    --discord-brand-hover: #4752c4;
    --discord-green: #3ba55c;
    --discord-red: #ed4245;
    --discord-yellow: #fee75c;
    --discord-message-hover: rgba(4, 4, 5, 0.07);
    --discord-divider: #202225;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'gg sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    background-color: var(--discord-bg-primary);
    color: var(--discord-text-normal);
    height: 100vh;
    overflow: hidden;
}

.discord-app {
    display: flex;
    height: 100vh;
    width: 100vw;
}

/* 侧边栏 (服务器列表模拟) */
.server-sidebar {
    width: 72px;
    background-color: var(--discord-bg-tertiary);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 12px;
    gap: 8px;
    flex-shrink: 0;
}

.server-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: var(--discord-bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.server-icon:hover {
    border-radius: 16px;
    background-color: var(--discord-brand);
}

.server-icon.active {
    border-radius: 16px;
    background-color: var(--discord-brand);
}

.server-icon.active::before {
    content: '';
    position: absolute;
    left: -16px;
    top: 10px;
    bottom: 10px;
    width: 8px;
    background-color: white;
    border-radius: 0 4px 4px 0;
}

.server-icon img {
    width: 100%;
    height: 100%;
    border-radius: inherit;
    object-fit: cover;
}

.server-separator {
    width: 32px;
    height: 2px;
    background-color: var(--discord-bg-primary);
    border-radius: 1px;
}

/* 频道列表 */
.channel-sidebar {
    width: 240px;
    background-color: var(--discord-bg-secondary);
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
}

.channel-header {
    height: 48px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    font-weight: 600;
    color: var(--discord-text-header);
    box-shadow: 0 1px 0 rgba(4, 4, 5, 0.2), 0 1.5px 0 rgba(6, 6, 7, 0.05), 0 2px 0 rgba(4, 4, 5, 0.05);
    cursor: pointer;
}

.channel-list {
    padding: 8px;
    flex: 1;
    overflow-y: auto;
}

.channel-category {
    padding-top: 16px;
    padding-left: 8px;
    padding-bottom: 4px;
    font-size: 12px;
    font-weight: 700;
    color: var(--discord-text-muted);
    text-transform: uppercase;
}

.channel-item {
    display: flex;
    align-items: center;
    padding: 6px 8px;
    margin: 1px 0;
    border-radius: 4px;
    color: var(--discord-text-muted);
    cursor: pointer;
    font-weight: 500;
}

.channel-item:hover {
    background-color: rgba(79, 84, 92, 0.16);
    color: var(--discord-text-normal);
}

.channel-item.active {
    background-color: rgba(79, 84, 92, 0.32);
    color: var(--discord-text-header);
}

.channel-hash {
    color: var(--discord-text-muted);
    margin-right: 6px;
    font-size: 20px;
    line-height: 20px;
}

/* 主聊天区域 */
.chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: var(--discord-bg-primary);
    min-width: 0;
    /* 防止flex子项溢出 */
}

.chat-header {
    height: 48px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    box-shadow: 0 1px 0 rgba(4, 4, 5, 0.2), 0 1.5px 0 rgba(6, 6, 7, 0.05), 0 2px 0 rgba(4, 4, 5, 0.05);
    flex-shrink: 0;
}

.chat-header-hash {
    color: var(--discord-text-muted);
    margin-right: 8px;
    font-size: 24px;
}

.chat-header-title {
    font-weight: 700;
    color: var(--discord-text-header);
    margin-right: 8px;
}

.chat-header-desc {
    color: var(--discord-text-muted);
    font-size: 14px;
    border-left: 1px solid var(--discord-text-muted);
    padding-left: 8px;
}

/* 消息流 */
.messages-wrapper {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    padding-bottom: 16px;
}

.messages-wrapper::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    background-color: var(--discord-bg-secondary);
}

.messages-wrapper::-webkit-scrollbar-thumb {
    background-color: var(--discord-bg-tertiary);
    border-radius: 4px;
}

.message-group {
    margin-top: 17px;
    padding: 2px 16px;
    position: relative;
}

.message-group:hover {
    background-color: var(--discord-message-hover);
}

.message-header {
    display: flex;
    align-items: baseline;
    margin-bottom: 4px;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 16px;
    margin-top: 2px;
    position: absolute;
    left: 16px;
    cursor: pointer;
}

.message-content-wrapper {
    margin-left: 56px;
}

.username {
    font-weight: 500;
    color: var(--discord-text-header);
    margin-right: 8px;
    cursor: pointer;
}

.username:hover {
    text-decoration: underline;
}

.bot-tag {
    background-color: var(--discord-brand);
    color: white;
    font-size: 10px;
    padding: 1px 4px;
    border-radius: 3px;
    vertical-align: middle;
    margin-right: 8px;
    font-weight: 500;
}

.timestamp {
    font-size: 12px;
    color: var(--discord-text-muted);
}

.message-text {
    color: var(--discord-text-normal);
    font-size: 16px;
    line-height: 1.375rem;
    white-space: pre-wrap;
}

.mention {
    background-color: rgba(88, 101, 242, 0.3);
    color: #dee0fc;
    border-radius: 3px;
    padding: 0 2px;
    font-weight: 500;
    cursor: pointer;
}

.mention:hover {
    background-color: var(--discord-brand);
    color: white;
}

/* Midjourney 风格图片网格 */
.mj-result-container {
    margin-top: 8px;
    max-width: 500px;
    background-color: var(--discord-bg-secondary);
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid var(--discord-bg-tertiary);
}

.mj-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2px;
    background-color: var(--discord-bg-primary);
    border-bottom: 1px solid var(--discord-bg-tertiary);
}

.mj-grid-item {
    position: relative;
    aspect-ratio: 1;
    cursor: pointer;
    overflow: hidden;
}

.mj-grid-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.2s;
}

.mj-grid-item:hover img {
    transform: scale(1.05);
}

/* 按钮组 */
.mj-actions {
    padding: 8px;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    background-color: var(--discord-bg-secondary);
}

.mj-btn {
    background-color: #4f545c;
    color: white;
    border: none;
    border-radius: 3px;
    padding: 6px 0;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
}

.mj-btn:hover {
    background-color: #686d73;
}

.mj-btn.primary {
    background-color: var(--discord-brand);
}

.mj-btn.primary:hover {
    background-color: var(--discord-brand-hover);
}

.mj-btn.reroll {
    grid-column: span 4;
    /* 占据整行 */
    background-color: #4f545c;
}

/* 输入区域 */
.input-area {
    padding: 0 16px 24px 16px;
    flex-shrink: 0;
}

.input-wrapper {
    background-color: #40444b;
    border-radius: 8px;
    padding: 11px 16px;
    display: flex;
    align-items: center;
}

.input-add-btn {
    color: var(--discord-interactive-normal);
    cursor: pointer;
    margin-right: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background-color: var(--discord-interactive-normal);
    border-radius: 50%;
    color: var(--discord-bg-primary);
    font-weight: bold;
    font-size: 16px;
}

.command-input {
    flex: 1;
    background: transparent;
    border: none;
    color: var(--discord-text-normal);
    font-size: 16px;
    font-family: inherit;
    height: 22px;
}

.command-input:focus {
    outline: none;
}

.command-input::placeholder {
    color: var(--discord-text-muted);
}

/* 模拟 /imagine 命令高亮 */
.command-pill {
    background-color: rgba(88, 101, 242, 0.3);
    color: #dee0fc;
    border-radius: 3px;
    padding: 2px 6px;
    margin-right: 8px;
    font-family: 'Consolas', monospace;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
}

.command-pill .cmd-name {
    font-weight: 700;
    margin-right: 4px;
}

/* 欢迎界面 */
.welcome-screen {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 20px;
}

.welcome-icon {
    width: 80px;
    height: 80px;
    background-color: var(--discord-bg-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    font-size: 40px;
}

.welcome-title {
    font-size: 32px;
    font-weight: 700;
    color: var(--discord-text-header);
    margin-bottom: 8px;
}

.welcome-subtitle {
    color: var(--discord-text-muted);
    max-width: 400px;
    line-height: 1.5;
}

/* 模态框 (查看大图) */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.85);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s;
}

.modal-overlay.active {
    opacity: 1;
    pointer-events: auto;
}

.modal-content {
    max-width: 90vw;
    max-height: 90vh;
    position: relative;
}

.modal-image {
    max-width: 100%;
    max-height: 90vh;
    border-radius: 4px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

.modal-close {
    position: absolute;
    top: -40px;
    right: 0;
    color: white;
    overflow: hidden;
}

.mj-grid-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.2s;
}

.mj-grid-item:hover img {
    transform: scale(1.05);
}

/* 按钮组 */
.mj-actions {
    padding: 8px;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    background-color: var(--discord-bg-secondary);
}

.mj-btn {
    background-color: #4f545c;
    color: white;
    border: none;
    border-radius: 3px;
    padding: 6px 0;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
}

.mj-btn:hover {
    background-color: #686d73;
}

.mj-btn.primary {
    background-color: var(--discord-brand);
}

.mj-btn.primary:hover {
    background-color: var(--discord-brand-hover);
}

.mj-btn.reroll {
    grid-column: span 4;
    /* 占据整行 */
    background-color: #4f545c;
}

/* 输入区域 */
.input-area {
    padding: 0 16px 24px 16px;
    flex-shrink: 0;
}

.input-wrapper {
    background-color: #40444b;
    border-radius: 8px;
    padding: 11px 16px;
    display: flex;
    align-items: center;
}

.input-add-btn {
    color: var(--discord-interactive-normal);
    cursor: pointer;
    margin-right: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background-color: var(--discord-interactive-normal);
    border-radius: 50%;
    color: var(--discord-bg-primary);
    font-weight: bold;
    font-size: 16px;
}

.command-input {
    flex: 1;
    background: transparent;
    border: none;
    color: var(--discord-text-normal);
    font-size: 16px;
    font-family: inherit;
    height: 22px;
}

.command-input:focus {
    outline: none;
}

.command-input::placeholder {
    color: var(--discord-text-muted);
}

/* 模拟 /imagine 命令高亮 */
.command-pill {
    background-color: rgba(88, 101, 242, 0.3);
    color: #dee0fc;
    border-radius: 3px;
    padding: 2px 6px;
    margin-right: 8px;
    font-family: 'Consolas', monospace;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
}

.command-pill .cmd-name {
    font-weight: 700;
    margin-right: 4px;
}

/* 欢迎界面 */
.welcome-screen {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 20px;
}

.welcome-icon {
    width: 80px;
    height: 80px;
    background-color: var(--discord-bg-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    font-size: 40px;
}

.welcome-title {
    font-size: 32px;
    font-weight: 700;
    color: var(--discord-text-header);
    margin-bottom: 8px;
}

.welcome-subtitle {
    color: var(--discord-text-muted);
    max-width: 400px;
    line-height: 1.5;
}

/* 模态框 (查看大图) */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.85);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s;
}

.modal-overlay.active {
    opacity: 1;
    pointer-events: auto;
}

.modal-content {
    max-width: 90vw;
    max-height: 90vh;
    position: relative;
}

.modal-image {
    max-width: 100%;
    max-height: 90vh;
    border-radius: 4px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

.modal-close {
    position: absolute;
    top: -40px;
    right: 0;
    color: white;
    font-size: 24px;
    cursor: pointer;
    opacity: 0.7;
}

.modal-close:hover {
    opacity: 1;
}

/* 参数控制面板样式 */
.param-section {
    padding: 12px;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    margin: 8px 0;
}

.param-group {
    margin-bottom: 12px;
}

.param-group:last-child {
    margin-bottom: 0;
}

.param-label {
    display: block;
    font-size: 12px;
    color: var(--discord-text-muted);
    margin-bottom: 6px;
    font-weight: 600;
}

.param-select,
.param-input {
    width: 100%;
    padding: 8px;
    background-color: var(--discord-bg-tertiary);
    border: 1px solid var(--discord-bg-primary);
    border-radius: 4px;
    color: var(--discord-text-normal);
    font-size: 14px;
    outline: none;
}

.param-select:focus,
.param-input:focus {
    border-color: var(--discord-brand);
}

.param-btn {
    padding: 8px;
    background-color: var(--discord-bg-tertiary);
    border: 1px solid var(--discord-bg-primary);
    border-radius: 4px;
    color: var(--discord-text-normal);
    cursor: pointer;
    transition: background-color 0.2s;
    min-width: 36px;
}

.param-btn:hover {
    background-color: #4f545c;
}

.param-upload-btn {
    width: 100%;
    padding: 8px;
    background-color: var(--discord-brand);
    border: none;
    border-radius: 4px;
    color: white;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 13px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.param-upload-btn:hover {
    background-color: var(--discord-brand-hover);
}

.uploaded-preview {
    margin-top: 8px;
    position: relative;
    border-radius: 4px;
    overflow: hidden;
}

.uploaded-preview img {
    width: 100%;
    height: auto;
    display: block;
}

.remove-img-btn {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 24px;
    height: 24px;
    background-color: rgba(255, 0, 0, 0.8);
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.remove-img-btn:hover {
    background-color: rgba(255, 0, 0, 1);
}

/* 参考图片预览（在消息中） */
.ref-preview {
    margin-top: 8px;
    max-width: 300px;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid var(--discord-bg-tertiary);
}

.ref-preview img {
    width: 100%;
    height: auto;
    display: block;
}

/* 发送按钮样式 */
.send-btn {
    background-color: var(--discord-brand);
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 8px;
    transition: background-color 0.2s;
}

.send-btn:hover:not(:disabled) {
    background-color: var(--discord-brand-hover);
}

.send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 输入框调整 */
.input-wrapper {
    background-color: #40444b;
    border-radius: 8px;
    padding: 8px 12px;
    display: flex;
    align-items: center;
}

.command-input {
    flex: 1;
    background: transparent;
    border: none;
    color: var(--discord-text-normal);
    font-size: 16px;
    font-family: inherit;
    padding: 4px;
}