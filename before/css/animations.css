/* 淡入动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.header {
    animation: fadeIn 0.8s ease-out;
}

.tools-grid .tool-card {
    animation: fadeIn 0.8s ease-out backwards;
    /* 动画只执行一次,避免悬浮时重复触发 */
    animation-fill-mode: forwards;
}

.tools-grid .tool-card:nth-child(1) {
    animation-delay: 0.1s;
}

.tools-grid .tool-card:nth-child(2) {
    animation-delay: 0.2s;
}

.tools-grid .tool-card:nth-child(3) {
    animation-delay: 0.3s;
}

.tools-grid .tool-card:nth-child(4) {
    animation-delay: 0.4s;
}

/* 图标悬浮动画 */
@keyframes iconFloat {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

.tool-card:hover .tool-icon {
    animation: iconFloat 2s ease-in-out infinite;
}
