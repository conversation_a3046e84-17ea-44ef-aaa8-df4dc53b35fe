<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能会议转写 - AI工具导航</title>
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/voice-recognition.css">
    <link rel="stylesheet" href="../css/voiceprint-modal.css">
</head>

<body>
<div id="app" class="pro-interface">
<header class="pro-header">
    <div class="header-left">
        <a href="index.html" class="back-link">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M19 12H5M12 19l-7-7 7-7" />
            </svg>
            <span>返回</span>
        </a>
        <div class="divider"></div>
        <h1 class="app-title">智能会议转写</h1>
    </div>
    <div class="header-right">
        <div class="status-badge" id="statusBadge">
            <div class="status-dot"></div>
            <span class="status-text">准备就绪</span>
        </div>
        <button class="icon-btn" id="voiceprintManage" title="声纹管理">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
        </button>
        <button class="icon-btn" id="tipsToggle" title="使用帮助">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                <line x1="12" y1="17" x2="12.01" y2="17"></line>
            </svg>
        </button>
    </div>
</header>

<!-- 核心文档区域 -->
<main class="document-area" id="documentArea">
    <div class="document-content" id="resultsList">
        <!-- 空状态 -->
        <div class="empty-state">
            <div class="empty-icon">🎙️</div>
            <h2>开始您的语音转写</h2>
            <p>点击下方麦克风按钮，实时将语音转换为文字</p>
            <div class="features-grid">
                <div class="feature-item">
                    <span class="icon">👥</span>
                    <span>多人分离</span>
                </div>
                <div class="feature-item">
                    <span class="icon">⚡</span>
                    <span>实时上屏</span>
                </div>
                <div class="feature-item">
                    <span class="icon">📝</span>
                    <span>自动排版</span>
                </div>
            </div>
        </div>
        <!-- 转写内容将在这里动态生成 -->
    </div>
</main>

<!-- 底部控制台 -->
<footer class="control-console">
    <!-- 声纹波形图 -->
    <div class="waveform-container">
        <canvas id="waveformCanvas"></canvas>
    </div>

    <!-- 控制栏 -->
    <div class="control-bar">
        <div class="control-group left">
            <div class="timer" id="recordingTimer">00:00:00</div>
            <!-- 参会人数选择器（开始前配置） -->
            <div class="config-group" id="preRecordConfig">
                <label class="config-label">参会人数:</label>
                <select id="speakerCountSelect" class="config-select">
                    <option value="2">2人</option>
                    <option value="3" selected>3人</option>
                    <option value="4">4人</option>
                    <option value="5">5人</option>
                    <option value="6">6人</option>
                    <option value="7">7人</option>
                    <option value="8">8人</option>
                    <option value="9">9人</option>
                    <option value="10">10人</option>
                </select>
                <label class="config-label" style="margin-left: 12px;">
                    <input type="checkbox" id="enableVoiceprint" class="config-checkbox">
                    启用声纹匹配
                </label>
            </div>
        </div>

        <div class="control-group center">
            <button id="startBtn" class="main-action-btn" title="开始录音">
                <div class="btn-inner">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.66 9 5v6c0 1.66 1.34 3 3 3z" />
                        <path
                            d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z" />
                    </svg>
                </div>
            </button>

            <button id="stopBtn" class="main-action-btn stop" disabled title="停止录音">
                <div class="btn-inner">
                    <div class="stop-square"></div>
                </div>
            </button>
        </div>

        <div class="control-group right">
            <button id="clearBtn" class="tool-btn" title="清空">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="3 6 5 6 21 6"></polyline>
                    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2">
                    </path>
                </svg>
            </button>
            <button id="copyAllBtn" class="tool-btn" title="复制全部">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                </svg>
            </button>
            <button id="exportBtn" class="tool-btn" title="导出文本">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                    <polyline points="7 10 12 15 17 10"></polyline>
                    <line x1="12" y1="15" x2="12" y2="3"></line>
                </svg>
            </button>
        </div>
    </div>
</footer>

<!-- 声纹管理模态框 -->
<div class="modal-overlay" id="voiceprintModal">
    <div class="modal-content modal-voiceprint">
        <div class="modal-header">
            <h3>🔊 声纹管理</h3>
            <button class="close-modal" id="closeVoiceprintBtn">×</button>
        </div>
        <div class="modal-body">
            <!-- 阈值调整 -->
            <div class="threshold-section">
                <label class="threshold-label" id="thresholdLabel">匹配阈值 (0.6)</label>
                <input type="range" id="thresholdSlider" min="0.3" max="1.0" step="0.05" value="0.6"
                    class="threshold-slider">
                <div class="threshold-tips">
                    <span>更宽松 (0.3)</span>
                    <span>更严格 (1.0)</span>
                </div>
            </div>

            <!-- 声纹列表 -->
            <div class="voiceprint-list" id="voiceprintList">
                <div class="list-header">已录入声纹</div>
                <div class="empty-voiceprints">
                    暂无声纹，点击下方按钮录入
                </div>
            </div>

            <!-- 录入新声纹 -->
            <div class="add-voiceprint-section">
                <button id="showRecorderBtn" class="add-vp-btn">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                        <line x1="12" y1="5" x2="12" y2="19"></line>
                        <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                    录入新声纹
                </button>

                <!-- 录音界面 -->
                <div class="recorder-panel" id="recorderPanel" style="display: none;">
                    <div class="recorder-input-group">
                        <label>姓名:</label>
                        <input type="text" id="userNameInput" placeholder="请输入姓名" class="vp-input">
                    </div>
                    <div class="recorder-controls">
                        <button id="recordVoiceprintBtn" class="record-btn">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <circle cx="12" cy="12" r="10"></circle>
                            </svg>
                            <span id="recordBtnText">点击录音 (3-5秒)</span>
                        </button>
                        <div id="recordingStatus" class="recording-status"></div>
                    </div>
                    <div class="recorder-actions">
                        <button id="cancelRecordBtn" class="secondary-btn">取消</button>
                        <button id="confirmVoiceprintBtn" class="primary-btn" disabled>确认录入</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 帮助弹窗 -->
<div class="modal-overlay" id="helpModal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>使用指南</h3>
            <button class="close-modal" id="closeHelpBtn">×</button>
        </div>
        <div class="modal-body">
            <ul class="help-list">
                <li>
                    <span class="icon">🎤</span>
                    <div class="text">
                        <h4>麦克风权限</h4>
                        <p>首次使用请允许浏览器访问麦克风</p>
                    </div>
                </li>
                <li>
                    <span class="icon">⌨️</span>
                    <div class="text">
                        <h4>快捷键操作</h4>
                        <p>空格键：开始/停止录音</p>
                    </div>
                </li>
                <li>
                    <span class="icon">👥</span>
                    <div class="text">
                        <h4>角色分离</h4>
                        <p>系统自动区分不同说话人</p>
                    </div>
                </li>
                <li>
                    <span class="icon">🔊</span>
                    <div class="text">
                        <h4>声纹识别</h4>
                        <p>预先录入声纹可提高识别准确度</p>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</div>
</div>

<script src="https://cdn.jsdelivr.net/npm/lamejs@1.2.1/lame.min.js"></script>
<script src="../js/voiceprint-manager.js"></script>
<script src="../js/voice-recognition.js"></script>
</body>

</html>