<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目质量 - 工作流视图</title>
    <link rel="stylesheet" href="../css/project-quality.css">
</head>

<body>
    <div id="app">
        <!-- 顶部菜单栏 -->
        <div class="menu-bar">
            <div class="menu-item">文件</div>
            <div class="menu-item">编辑</div>
            <div class="menu-item">视图</div>
            <div class="menu-item">运行</div>
            <div class="spacer"></div>
            <div class="menu-item action-btn">排队提示词</div>
            <div class="menu-item action-btn secondary">额外选项</div>
        </div>

        <!-- 工作区 -->
        <div class="workspace" id="workspace">
            <!-- 背景网格和连线画布 -->
            <canvas id="connections-canvas"></canvas>

            <!-- 节点容器 -->
            <div id="nodes-container">
                <!-- 节点将通过 JS 动态生成 -->
            </div>
        </div>

        <!-- 侧边控制面板 (可选，类似 ComfyUI 的右侧面板) -->
        <div class="control-panel">
            <button id="btn-clear">清空工作流</button>
            <button id="btn-load-default">加载默认</button>
            <div class="panel-section">
                <h3>运行状态</h3>
                <p>就绪</p>
            </div>
        </div>
    </div>

    <!-- 引入 Vue (虽然主要逻辑可能手写，但保持项目一致性可以引入，或者纯原生JS实现节点逻辑更轻量) -->
    <!-- 这里为了高性能节点交互，主要使用原生 JS -->
    <script src="../js/project-quality.js"></script>
</body>

</html>