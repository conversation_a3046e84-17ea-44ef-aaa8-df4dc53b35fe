<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音识别测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #1a1a2e;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        button {
            padding: 15px 30px;
            margin: 10px;
            font-size: 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
        }
        .start { background: #4CAF50; color: white; }
        .stop { background: #f44336; color: white; }
        #log {
            margin-top: 20px;
            padding: 15px;
            background: #000;
            border-radius: 8px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 3px 0;
            padding: 3px;
        }
        .info { color: #0f0; }
        .error { color: #f00; }
        .warn { color: #ff0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 语音识别功能测试</h1>
        
        <div>
            <button class="start" onclick="testStart()">开始测试</button>
            <button class="stop" onclick="clearLog()">清空日志</button>
        </div>
        
        <div id="log"></div>
    </div>

    <script>
        // 日志输出函数
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 测试函数
        async function testStart() {
            log('========== 开始测试 ==========', 'info');
            
            // 1. 检查浏览器环境
            log(`浏览器: ${navigator.userAgent}`, 'info');
            log(`协议: ${window.location.protocol}`, 'info');
            log(`主机: ${window.location.hostname}`, 'info');
            
            // 2. 检查API支持
            log('检查API支持...', 'info');
            
            if (!navigator.mediaDevices) {
                log('❌ navigator.mediaDevices 不存在', 'error');
                
                // 尝试polyfill
                if (navigator.getUserMedia || navigator.webkitGetUserMedia || navigator.mozGetUserMedia) {
                    log('⚠️ 检测到旧版API,尝试polyfill...', 'warn');
                    navigator.mediaDevices = {};
                    navigator.mediaDevices.getUserMedia = function(constraints) {
                        const getUserMedia = navigator.getUserMedia || 
                                           navigator.webkitGetUserMedia || 
                                           navigator.mozGetUserMedia;
                        
                        if (!getUserMedia) {
                            return Promise.reject(new Error('getUserMedia不支持'));
                        }
                        
                        return new Promise((resolve, reject) => {
                            getUserMedia.call(navigator, constraints, resolve, reject);
                        });
                    };
                    log('✅ Polyfill成功', 'info');
                } else {
                    log('❌ 浏览器完全不支持录音API', 'error');
                    alert('您的浏览器不支持录音功能!\n\n请使用Chrome、Safari、Edge等现代浏览器');
                    return;
                }
            } else {
                log('✅ navigator.mediaDevices 存在', 'info');
            }
            
            if (!navigator.mediaDevices.getUserMedia) {
                log('❌ getUserMedia 不存在', 'error');
                return;
            } else {
                log('✅ getUserMedia 存在', 'info');
            }
            
            if (!window.WebSocket) {
                log('❌ WebSocket 不支持', 'error');
                return;
            } else {
                log('✅ WebSocket 支持', 'info');
            }
            
            // 3. 测试麦克风权限
            log('请求麦克风权限...', 'info');
            
            try {
                // 先尝试标准配置
                let stream;
                try {
                    stream = await navigator.mediaDevices.getUserMedia({
                        audio: {
                            channelCount: 1,
                            sampleRate: 16000,
                            echoCancellation: true
                        }
                    });
                    log('✅ 标准配置获取成功', 'info');
                } catch (err) {
                    log(`⚠️ 标准配置失败: ${err.message}`, 'warn');
                    log('尝试基础配置...', 'info');
                    stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                    log('✅ 基础配置获取成功', 'info');
                }
                
                // 获取音频轨道信息
                const audioTrack = stream.getAudioTracks()[0];
                const settings = audioTrack.getSettings();
                log(`音频设备: ${audioTrack.label}`, 'info');
                log(`采样率: ${settings.sampleRate || '未知'}Hz`, 'info');
                log(`声道数: ${settings.channelCount || '未知'}`, 'info');
                
                // 4. 测试AudioContext
                log('测试AudioContext...', 'info');
                const AudioContextClass = window.AudioContext || window.webkitAudioContext;
                
                if (!AudioContextClass) {
                    log('❌ AudioContext 不支持', 'error');
                    stream.getTracks().forEach(track => track.stop());
                    return;
                } else {
                    log('✅ AudioContext 支持', 'info');
                }
                
                let audioContext;
                try {
                    audioContext = new AudioContextClass({ sampleRate: 16000 });
                } catch (err) {
                    log(`⚠️ 自定义采样率失败: ${err.message}`, 'warn');
                    audioContext = new AudioContextClass();
                }
                
                log(`AudioContext采样率: ${audioContext.sampleRate}Hz`, 'info');
                
                // 5. 测试WebSocket连接
                log('测试WebSocket连接...', 'info');
                const ws = new WebSocket('ws://127.0.0.1:8000/ws/voice-recognition');
                
                ws.onopen = () => {
                    log('✅ WebSocket连接成功', 'info');
                    log('========== 所有测试通过! ==========', 'info');
                    
                    // 清理资源
                    setTimeout(() => {
                        ws.close();
                        stream.getTracks().forEach(track => track.stop());
                        audioContext.close();
                        log('资源已清理', 'info');
                    }, 1000);
                };
                
                ws.onerror = (error) => {
                    log(`❌ WebSocket连接失败: ${error}`, 'error');
                    log('请检查后端服务是否启动(运行"启动项目.bat")', 'warn');
                    stream.getTracks().forEach(track => track.stop());
                    audioContext.close();
                };
                
                ws.onclose = () => {
                    log('WebSocket已关闭', 'info');
                };
                
            } catch (error) {
                log(`❌ 错误: ${error.name}`, 'error');
                log(`详情: ${error.message}`, 'error');
                
                // 根据错误类型给出提示
                if (error.name === 'NotAllowedError') {
                    alert('❌ 麦克风权限被拒绝\n\n请在浏览器设置中允许麦克风权限');
                } else if (error.name === 'NotFoundError') {
                    alert('❌ 未检测到麦克风设备');
                } else if (error.name === 'SecurityError') {
                    alert('❌ 安全限制\n\n请使用HTTPS访问或localhost');
                } else {
                    alert('❌ 发生错误:\n' + error.message);
                }
            }
        }
        
        // 页面加载完成自动显示环境信息
        window.addEventListener('load', () => {
            log('页面加载完成', 'info');
            log('点击"开始测试"按钮运行诊断', 'warn');
        });
    </script>
</body>
</html>
