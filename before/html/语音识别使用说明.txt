========================================
   🎤 实时语音识别 - 使用说明
========================================

【问题已修复】
✅ JavaScript语法错误已修复
✅ 现在按钮点击会有响应了

【使用步骤】

1. 启动后端服务
   - 运行根目录下的"启动项目.bat"
   - 确认看到"Uvicorn running on http://127.0.0.1:8000"

2. 打开网页
   PC端: 
   - 直接打开 before/html/voice-recognition.html
   
   手机端:
   - 需要通过HTTPS访问或使用内网IP
   - 例如: http://192.168.x.x:8000/...

3. 测试功能
   ▶ 先打开测试页面验证环境:
     before/html/test-voice.html
   
   ▶ 点击"开始测试"按钮,查看诊断结果
   
   ▶ 如果测试通过,再打开正式页面:
     before/html/voice-recognition.html

4. 开始录音
   - 点击"开始录音"按钮
   - 允许浏览器的麦克风权限
   - 开始说话,实时显示识别结果

【手机端调试】

如果点击没反应:
1. 双击页面标题"实时语音识别"
2. 开启调试面板(页面底部绿色文字)
3. 查看详细日志信息
4. 截图发送给开发者

【常见问题】

Q: 点击按钮没反应
A: 刷新页面(Ctrl+F5强制刷新),清除缓存

Q: 提示权限被拒绝
A: 浏览器设置 → 隐私和安全 → 网站设置 → 麦克风 → 允许

Q: 手机上无法录音
A: 必须使用HTTPS访问,或者:
   - 微信/QQ内置浏览器可能支持HTTP
   - 确保不在隐私模式下

Q: WebSocket连接失败
A: 检查后端服务是否启动,防火墙是否拦截

【浏览器兼容性】

✅ 支持:
- Chrome 60+
- Safari 11+
- Edge 79+
- 微信内置浏览器
- QQ浏览器
- 百度浏览器

❌ 不支持:
- IE浏览器
- 低于上述版本的老旧浏览器

【文件说明】

test-voice.html - 测试诊断页面(推荐先打开这个)
voice-recognition.html - 正式功能页面
voice-recognition.js - 主要功能代码

========================================
            最后更新: 2025-11-17
========================================
